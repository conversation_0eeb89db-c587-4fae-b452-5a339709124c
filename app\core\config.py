"""
项目配置文件

包含数据库连接配置、API配置等核心设置
"""

from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional
from pathlib import Path


class Settings(BaseSettings):
    """应用配置设置"""
    
    # 应用基础配置
    app_name: str = "AlertGraph"
    app_version: str = "0.1.0"
    debug: bool = True
    
    # Neo4j数据库配置
    neo4j_uri: str = "bolt://localhost:7687"
    neo4j_user: str = "neo4j"
    neo4j_password: str = "firstpassword"
    neo4j_database: str = "neo4j"
    
    # API配置
    api_prefix: str = "/api/v1"
    docs_url: str = "/docs"
    redoc_url: str = "/redoc"
    
    # 分页配置
    default_page_size: int = 20
    max_page_size: int = 100
    
    # 日志配置
    log_level: str = "DEBUG"
    log_dir: str = "logs"
    log_file: Optional[str] = None
    log_format: str = "json"  # json or text
    environment: str = "development"
    log_max_size: str = "10MB"  # 日志文件最大大小
    log_backup_count: int = 15  # 备份文件数量，满足15天保留要求
    log_retention_days: int = 15  # 日志保留天数
    log_to_console: bool = True  # 是否输出到控制台
    log_to_file: bool = True     # 是否输出到文件
    
    def get_log_file_path(self) -> Path:
        """获取日志文件路径"""
        log_dir = Path(self.log_dir)
        log_dir.mkdir(exist_ok=True)
        return log_dir / f"{self.app_name.lower()}.log"
    
    def get_error_log_file_path(self) -> Path:
        """获取错误日志文件路径"""
        log_dir = Path(self.log_dir)
        log_dir.mkdir(exist_ok=True)
        return log_dir / f"{self.app_name.lower()}_error.log"
    
    def get_debug_log_file_path(self) -> Path:
        """获取调试日志文件路径"""
        log_dir = Path(self.log_dir)
        log_dir.mkdir(exist_ok=True)
        return log_dir / f"{self.app_name.lower()}_debug.log"
    
    # 根据环境自动调整日志级别
    def get_effective_log_level(self) -> str:
        """根据环境获取有效的日志级别"""
        if self.environment == "production":
            return "INFO"  # 生产环境禁止DEBUG日志
        elif self.environment == "testing":
            return "WARNING"  # 测试环境减少日志
        else:
            return self.log_level  # 开发环境使用配置的级别
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )


# 全局配置实例
settings = Settings() 