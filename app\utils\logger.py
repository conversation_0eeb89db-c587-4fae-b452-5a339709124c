"""
JSON日志管理系统

根据开发手册日志规约设计：
1. 避免重复打印日志
2. 生产环境禁止debug日志，谨慎记录info日志
3. 日志要有实际价值，便于问题排查
"""

import json
import logging
import time
import traceback
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from functools import wraps
from contextlib import contextmanager
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from pathlib import Path
import os

from app.core.config import settings


class JsonFormatter(logging.Formatter):
    """精简的JSON格式化器"""

    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
        }

        # 只在必要时添加详细信息
        if record.levelno >= logging.WARNING:
            log_data.update(
                {
                    "module": record.module,
                    "function": record.funcName,
                    "line": record.lineno,
                }
            )

        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info),
            }

        # 添加额外字段
        if hasattr(record, "extra_data"):
            log_data.update(record.extra_data)

        return json.dumps(log_data, ensure_ascii=False, default=str)


class AlertGraphLogger:
    """AlertGraph日志器"""

    def __init__(self, name: str = "alertgraph"):
        self.logger = logging.getLogger(name)
        self._last_request_time = {}  # 防重复日志
        self._setup_logger()

    def _setup_logger(self):
        """设置日志器"""
        if self.logger.handlers:
            return  # 避免重复设置

        # 根据环境设置日志级别
        effective_level = settings.get_effective_log_level()
        self.logger.setLevel(getattr(logging, effective_level.upper()))

        # 控制台处理器 - 简化输出
        if settings.log_to_console and settings.environment == "development":
            console_handler = logging.StreamHandler()
            simple_formatter = logging.Formatter(
                "%(asctime)s [%(levelname)s] %(message)s", datefmt="%H:%M:%S"
            )
            console_handler.setFormatter(simple_formatter)
            console_handler.setLevel(logging.INFO)
            self.logger.addHandler(console_handler)

        # 文件处理器 - 使用按时间轮转
        if settings.log_to_file:
            # 主日志文件 - 按天轮转
            main_log_path = settings.get_log_file_path()
            main_handler = TimedRotatingFileHandler(
                main_log_path,
                when="midnight",
                interval=1,
                backupCount=settings.log_retention_days,
                encoding="utf-8",
            )
            main_handler.setLevel(logging.INFO)
            main_handler.setFormatter(JsonFormatter())
            self.logger.addHandler(main_handler)

            # 错误日志文件 - 按天轮转
            error_log_path = settings.get_error_log_file_path()
            error_handler = TimedRotatingFileHandler(
                error_log_path,
                when="midnight",
                interval=1,
                backupCount=settings.log_retention_days,
                encoding="utf-8",
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(JsonFormatter())
            self.logger.addHandler(error_handler)

    def _should_log_request(self, path: str, method: str) -> bool:
        """判断是否应该记录请求日志（避免重复）"""
        # 健康检查等高频请求不记录
        if path in ["/health", "/docs", "/openapi.json"]:
            return False

        # 防止短时间内重复记录相同请求
        key = f"{method}:{path}"
        current_time = time.time()
        last_time = self._last_request_time.get(key, 0)

        if current_time - last_time < 1:  # 1秒内相同请求不重复记录
            return False

        self._last_request_time[key] = current_time
        return True

    def _log_with_extra(self, level: str, message: str, **kwargs):
        """带额外数据的日志记录"""
        extra_data = {
            "service": "alertgraph",
            "environment": settings.environment,
            **kwargs,
        }

        record = self.logger.makeRecord(
            self.logger.name,
            getattr(logging, level.upper()),
            "",  # pathname
            0,  # lineno
            message,
            (),  # args
            None,  # exc_info
        )
        record.extra_data = extra_data
        self.logger.handle(record)

    def info(self, message: str, **kwargs):
        """信息日志 - 谨慎使用"""
        self._log_with_extra("INFO", message, **kwargs)

    def debug(self, message: str, **kwargs):
        """调试日志 - 生产环境不输出"""
        if settings.environment != "production":
            self._log_with_extra("DEBUG", message, **kwargs)

    def warning(self, message: str, **kwargs):
        """警告日志"""
        self._log_with_extra("WARNING", message, **kwargs)

    def error(self, message: str, error: Exception = None, **kwargs):
        """错误日志 - 提供有用的错误信息"""
        if error:
            kwargs.update(
                {"error_type": type(error).__name__, "error_message": str(error)}
            )
            # 只在开发环境记录完整堆栈
            if settings.environment == "development":
                kwargs["traceback"] = traceback.format_exc()
        self._log_with_extra("ERROR", message, **kwargs)

    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self._log_with_extra("CRITICAL", message, **kwargs)

    def api_request(self, method: str, path: str, **kwargs):
        """API请求日志 - 简化并避免重复"""
        if not self._should_log_request(path, method):
            return

        # 只记录关键信息
        essential_kwargs = {
            k: v
            for k, v in kwargs.items()
            if k in ["client_ip", "user_id", "request_id"]
        }

        self._log_with_extra(
            "INFO",
            f"{method} {path}",
            request_method=method,
            request_path=path,
            **essential_kwargs,
        )

    def api_response(
        self, method: str, path: str, status_code: int, duration: float, **kwargs
    ):
        """API响应日志 - 只记录异常响应或慢请求"""
        should_log = (
            status_code >= 400  # 错误响应
            or duration > 5.0  # 慢请求(5秒以上)
            or self._should_log_request(path, method)  # 正常请求(去重)
        )

        if should_log:
            log_data = {
                "request_method": method,
                "request_path": path,
                "status_code": status_code,
                "duration_ms": round(duration * 1000, 2),
            }

            # 慢请求警告
            level = "WARNING" if duration > 5.0 else "INFO"
            message = f"{method} {path} - {status_code}"
            if duration > 5.0:
                message += f" (SLOW: {duration:.2f}s)"

            self._log_with_extra(level, message, **log_data)

    def business_operation(self, operation: str, **kwargs):
        """业务操作日志 - 合并相关操作"""
        # 只记录重要的业务操作
        important_operations = [
            "batch_create_alerts",
            "create_alert_detail",
            "create_verdict",
            "create_entity",
        ]

        if any(op in operation for op in important_operations):
            # 精简信息
            essential_kwargs = {
                k: v
                for k, v in kwargs.items()
                if k in ["entity_id", "alert_count", "duration_ms", "status"]
            }
            self._log_with_extra("INFO", f"业务操作: {operation}", **essential_kwargs)

    def database_error(self, operation: str, error: Exception, **kwargs):
        """数据库错误日志 - 专门记录数据库问题"""
        self.error(
            f"数据库操作失败: {operation}", error=error, operation=operation, **kwargs
        )


# 全局日志实例
logger = AlertGraphLogger()


def log_execution_time(operation_name: str = None, min_duration: float = 1.0):
    """
    装饰器：记录函数执行时间
    只记录超过最小时长的操作，避免无意义日志
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            operation = operation_name or f"{func.__name__}"

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # 只记录耗时较长的操作
                if duration >= min_duration:
                    logger.info(
                        f"操作完成: {operation}",
                        operation=operation,
                        duration_ms=round(duration * 1000, 2),
                        status="success",
                    )
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(
                    f"操作失败: {operation}",
                    error=e,
                    operation=operation,
                    duration_ms=round(duration * 1000, 2),
                    status="failed",
                )
                raise

        return wrapper

    return decorator


@contextmanager
def log_database_operation(operation: str):
    """上下文管理器：记录数据库操作 - 只记录错误"""
    start_time = time.time()

    try:
        yield
        # 成功的数据库操作不记录日志，避免过多无用信息
    except Exception as e:
        duration = time.time() - start_time
        logger.database_error(operation, e, duration_ms=round(duration * 1000, 2))
        raise


# 清理旧日志文件
def cleanup_old_logs():
    """清理超过保留期限的日志文件"""
    try:
        log_dir = Path(settings.log_dir)
        if not log_dir.exists():
            return

        cutoff_date = datetime.now() - timedelta(days=settings.log_retention_days)

        for log_file in log_dir.glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_date.timestamp():
                log_file.unlink()
                logger.debug(f"清理旧日志文件: {log_file}")
    except Exception as e:
        logger.error("清理日志文件失败", error=e)


# 便捷函数 - 简化版本
def log_info(message: str, **kwargs):
    """记录信息日志 - 谨慎使用"""
    logger.info(message, **kwargs)


def log_error(message: str, error: Exception = None, **kwargs):
    """记录错误日志"""
    logger.error(message, error=error, **kwargs)


def log_warning(message: str, **kwargs):
    """记录警告日志"""
    logger.warning(message, **kwargs)
