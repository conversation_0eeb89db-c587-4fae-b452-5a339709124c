# AlertGraph 图数据库系统开发文档

## 1. 项目概述

AlertGraph是基于Neo4j的安全告警知识图谱系统，支持告警数据存储、管理和查询，构建多层级告警关系图谱。

**技术栈**: Python 3.12 + FastAPI 0.115.12 + Neo4j 5.26.7-community

**核心功能**:
- 告警数据批量/流式写入
- 多层级告警关系构建
- 风险实体关联分析
- 实体去重机制
- 研判记录管理
- RESTful API服务

## 2. 系统架构

**分层架构设计**:
- **API层**: RESTful接口，支持前端和外部系统调用
- **业务逻辑层**: 告警关联、研判处理、图谱构建核心逻辑
- **数据访问层**: Repository模式封装Neo4j CRUD操作
- **数据模型层**: 图数据库节点和关系模型定义
- **数据库连接层**: Neo4j连接管理、连接池维护、事务处理

## 3. 数据模型

### 3.1 核心节点类型

#### 3.1.1 告警细节节点 (AlertDetail)
**功能**: 存储单个告警的详细信息，作为图谱的核心节点
**数据来源**: 外部安全设备推送的告警数据

**关键字段**:
- `vid`: 图数据库节点唯一ID (系统生成)
- `alert_detail_id`: 告警细节ID (必填)
- `alert_name/alert_message`: 告警名称和描述 (必填)
- `product_name`: 生成告警的设备名称 (必填)
- `confidence_score`: 置信度分数 0-100 (必填)
- `impact/risk/severity`: 影响等级/风险等级/严重性 (枚举值，必填)
- `status`: 处理状态 (New/InProgress/Suppressed/Resolved/Archived，默认New)
- `vulnerabilities`: 漏洞描述 (可选)
- `comment`: 评论 (可选)
- `raw_data`: 原始推送数据 (可选)
- `time/created_at/updated_at`: 时间戳字段 (系统生成)

**字段更新逻辑**:
- 创建时: 所有字段根据输入数据设置，时间戳字段由系统生成
- 更新时: 支持状态、评论等字段的修改，`updated_at`由系统更新

#### 3.1.2 研判记录节点 (Verdict)
**功能**: 存储对告警的研判结果，支持AI和人工研判
**数据来源**: 安全分析师手动研判或AI研判

**关键字段**:
- `verdict_id`: 研判记录唯一ID (系统生成)
- `type_id/type`: 研判类型 (1-AI/2-人工，必填)
- `label`: 研判标签 (1-误报/2-确认攻击/3-可疑/4-数据不足，必填)
- `reason`: 研判理由 (必填)
- `commiter`: 研判提交者 (必填)
- `comment`: 研判评论 (可选)
- `time/created_at/updated_at`: 时间戳字段 (系统生成)

**字段更新逻辑**:
- 创建时: 根据研判输入设置所有字段
- 支持对同一告警的多次研判，每次创建新的研判记录

#### 3.1.3 证据信息节点 (Evidence)
**功能**: 合并存储告警的所有证据制品，提取关键信息用于实体关联
**数据来源**: 告警数据中的evidence_artifacts字段

**关键字段**:
- `evidence_id`: 证据唯一ID (基于内容生成UUID5)
- `evidence_type`: 固定为"combined_evidence"
- `evidence_types`: 所有证据制品的类型列表
- `is_alert_trigger`: 是否包含告警触发证据 (布尔值)
- `include_payload`: 是否包含攻击载荷 (布尔值)
- `evidence_count`: 包含的证据制品数量
- `raw_evidence_data`: 原始证据数据JSON
- `created_time/updated_time`: 时间戳字段

**动态属性** (根据证据内容提取):
- `http_methods`: HTTP方法列表
- `domains`: 域名列表
- `ip_addresses`: IP地址列表
- `file_hashes`: 文件哈希列表
- `process_names`: 进程名称列表

**字段更新逻辑**:
- 创建时: 合并所有evidence_artifacts，提取关键信息到动态属性
- 不支持更新，每个告警对应唯一的证据节点

#### 3.1.4 设备信息节点 (Device)
**功能**: 存储生成告警的设备信息，支持设备级别的告警统计
**数据来源**: 从告警数据中提取设备相关字段

**关键字段**:
- `device_id`: 设备唯一标识 (基于hostname/ip/product_name生成)
- `uuid/org`: 设备UUID和所属组织 (可选)
- `ip/hostname/mac`: 网络标识信息 (可选)
- `os_name`: 操作系统名称 (可选)
- `count`: 告警数量统计 (默认1，每次关联告警时递增)
- `created_at/updated_at`: 时间戳字段

**字段更新逻辑**:
- 创建时: 根据告警数据提取设备信息，count初始化为1
- 更新时: 每次关联新告警时count递增，updated_at更新

#### 3.1.5 风险实体节点
**功能**: 从证据中提取的风险实体，支持去重和关联分析
**数据来源**: 从Evidence节点的证据制品中提取

**支持的实体类型**:
- `NetworkEndpointEntity`: 网络端点 (IP地址+端口)
- `FileEntity`: 文件 (文件哈希、文件名等)
- `ProcessEntity`: 进程 (进程名+命令行)
- `UrlEntity`: URL (完整URL或域名+路径)
- `DnsQueryEntity`: DNS查询 (查询域名+类型)
- `HttpRequestEntity`: HTTP请求 (方法+URL+User-Agent)
- `HttpResponseEntity`: HTTP响应 (状态码+内容类型+响应体哈希)

**通用字段**:
- `entity_id`: 实体唯一ID (系统生成)
- `name`: 实体名称 (用于显示)
- `reuse_count`: 复用次数 (初始为1，每次匹配到现有实体时递增)
- `risk_score`: 风险分数 0-100 (基于实体特征计算)
- `description`: 实体描述
- `created_at/updated_at`: 时间戳字段

**实体特定字段**:
- **NetworkEndpointEntity**: `ip_address`(主要匹配), `port`(辅助匹配), `endpoint_type`, `hostname`, `mac`, `domain`, `location`等
- **FileEntity**: `file_hash`(主要匹配), `filename`(备选匹配), `file_size`, `file_path`, `hash_md5/sha1/sha256`等
- **ProcessEntity**: `process_signature`(主要匹配), `process_name`(备选匹配), `process_path`, `command_line`, `user_name`等
- **UrlEntity**: `full_url`(主要匹配), `hostname`+`path`(辅助匹配), `domain`, `port`, `scheme`等
- **DnsQueryEntity**: `query_name`+`query_type`(主要匹配), `hostname`(备选匹配), `query_class`, `opcode`等
- **HttpRequestEntity**: `method`+`url`+`user_agent`(主要匹配), `method`+`url`(辅助匹配), `body`, `version`等
- **HttpResponseEntity**: `status_code`+`content_type`+`body_hash`(主要匹配), `status_code`+`content_type`(辅助匹配)等

**字段更新逻辑**:
- 创建时: 根据证据内容提取实体信息，设置初始字段值
- 复用时: `reuse_count`递增，`updated_at`更新，`risk_score`取历史最高值

### 3.2 关系类型定义

#### 3.2.1 HAS_EVIDENCE 关系
**方向**: AlertDetail -> Evidence
**功能**: 关联告警细节与其证据信息
**创建时机**: 告警创建时，如果包含evidence_artifacts

**关系属性**:
- `created_time`: 关系创建时间
- `is_trigger`: 证据是否为告警触发原因 (布尔值)
- `has_payload`: 证据是否包含攻击载荷 (布尔值)
- `evidence_count`: 证据制品数量

#### 3.2.2 RELATES_TO 关系
**方向**: Evidence -> Entity (各种风险实体)
**功能**: 关联证据信息与从中提取的风险实体
**创建时机**: 证据处理时，从证据制品中提取实体后创建

**关系属性**:
- `entity_type`: 实体类型 (如NetworkEndpointEntity)
- `created_time`: 关系创建时间
- `extraction_reason`: 固定为"从证据中提取"
- `is_new_entity`: 实体是否为新创建 (布尔值)
- `entity_context`: 实体上下文信息JSON (包含实体快照)

#### 3.2.3 GENERATED_BY 关系
**方向**: AlertDetail -> Device
**功能**: 关联告警细节与生成该告警的设备
**创建时机**: 告警创建时，从告警数据中提取设备信息后创建

**关系属性**:
- `created_time`: 关系创建时间
- `relationship_reason`: 固定为"告警由此设备生成"

#### 3.2.4 HAS_VERDICT 关系
**方向**: AlertDetail -> Verdict
**功能**: 关联告警细节与研判记录，支持多次研判
**创建时机**: 用户或AI对告警进行研判时创建

**关系属性**:
- `created_time`: 关系创建时间

**未实现关系** (预留):
- `BELONGS_TO`: AlertDetail -> AlertSession (告警会话)
- `CONTAINS`: AlertSession -> AlertEvent (告警事件)

## 4. 核心服务组件

### 4.1 数据库连接层

#### 4.1.1 Neo4jConnection
**功能**: Neo4j数据库连接管理、连接池维护、事务处理
**核心方法**:
- `get_driver()`: 获取数据库驱动
- `execute_query()`: 执行通用查询
- `execute_write_transaction()`: 执行写事务
- `execute_read_transaction()`: 执行读事务
- `health_check()`: 数据库健康检查

#### 4.1.2 BaseRepository
**功能**: 提供通用CRUD操作，封装Neo4j查询逻辑
**核心方法**:
- `create_node()`: 创建节点
- `get_node_by_id()`: 根据ID获取节点
- `update_node()`: 更新节点属性
- `create_relationship()`: 创建关系
- `batch_create_nodes()`: 批量创建节点

### 4.2 业务服务层

#### 4.2.1 AlertService
**功能**: 告警管理核心业务逻辑
**主要职责**:
- 告警细节创建和属性管理
- 告警状态更新和生命周期管理
- 告警查询和过滤
- 告警与其他实体的关联管理

**核心方法**:
- `create_alert_detail()`: 创建告警细节，触发证据处理和设备关联
- `batch_create_alerts()`: 批量创建告警
- `update_alert_status()`: 更新告警处理状态
- `search_alerts()`: 按条件查询告警列表

#### 4.2.2 VerdictService
**功能**: 研判记录管理
**主要职责**:
- 研判记录创建和管理
- 支持AI和人工研判
- 研判历史查询

**核心方法**:
- `create_verdict()`: 创建研判记录并关联告警
- `get_verdicts_by_alert()`: 获取告警的所有研判记录
- `add_verdict_to_alert()`: 为告警添加研判记录

#### 4.2.3 EvidenceService
**功能**: 证据信息处理和实体提取
**主要职责**:
- 合并多个evidence_artifacts为单个Evidence节点
- 从证据中提取风险实体
- 建立证据与实体的关联关系
- 动态属性提取和管理

**核心方法**:
- `process_evidence_artifacts()`: 处理证据制品，创建Evidence节点
- `extract_risk_entities()`: 从证据中提取风险实体
- `link_evidence_to_entity()`: 建立证据与实体的关联

#### 4.2.4 EntityService
**功能**: 风险实体管理和去重
**主要职责**:
- 实体创建和去重逻辑
- 基于规则的匹配算法实现
- 实体复用统计和更新
- 实体关联查询

**核心方法**:
- `find_or_create_entity()`: 实体去重的核心方法
- `_find_existing_entity()`: 匹配算法实现
- `get_match_criteria()`: 生成匹配条件
- `update_entity_on_reuse()`: 实体复用时的更新逻辑

#### 4.2.5 DeviceService
**功能**: 设备信息管理
**主要职责**:
- 从告警数据中提取设备信息
- 设备去重和统计
- 设备与告警的关联管理

**核心方法**:
- `extract_and_create_device()`: 提取设备信息并创建/更新设备节点
- `link_alert_to_device()`: 建立告警与设备的关联

### 4.3 REST API接口

**告警管理**:
- `POST /api/v1/alerts`: 创建告警细节
- `POST /api/v1/alerts/batch`: 批量创建告警细节
- `GET /api/v1/alerts/{alert_id}`: 获取告警详情
- `PUT /api/v1/alerts/{alert_id}`: 更新告警细节
- `GET /api/v1/alerts`: 按时间范围查询告警列表
- `GET /api/v1/alerts/device/{device_id}`: 获取设备的告警列表
- `GET /api/v1/alerts/{alert_id}/entities`: 获取告警相关的风险实体

**研判管理**:
- `POST /api/v1/verdicts`: 创建研判记录
- `GET /api/v1/verdicts/{verdict_id}`: 获取研判详情
- `PUT /api/v1/verdicts/{verdict_id}`: 更新研判记录
- `DELETE /api/v1/verdicts/{verdict_id}`: 删除研判记录
- `POST /api/v1/alerts/{alert_id}/verdicts`: 为告警添加研判记录
- `GET /api/v1/alerts/{alert_id}/verdicts`: 获取告警研判记录列表
- `GET /api/v1/alerts/{alert_id}/verdicts/latest`: 获取告警的最新研判记录

**系统管理**:
- `GET /api/v1/system/health`: 获取系统健康状态
- `GET /api/v1/system/stats`: 获取系统统计信息
- `GET /api/v1/system/database/stats`: 获取数据库统计信息

## 5. 核心业务流程

### 5.1 告警创建流程

**流程概述**: 接收外部告警数据，创建AlertDetail节点，处理证据信息，提取设备和实体信息，建立关联关系

**主要步骤**:
1. **数据验证**: 验证输入的告警数据格式和必填字段
2. **节点创建**: 生成唯一ID，创建AlertDetail节点
3. **证据处理**: 如果包含evidence_artifacts，创建Evidence节点并提取风险实体
4. **设备关联**: 从告警数据提取设备信息，创建/更新Device节点并建立关联
5. **关系建立**: 创建AlertDetail与Evidence、Device的关系
6. **响应返回**: 返回创建的告警详情

**关键字段处理**:
- `vid`: 系统生成UUID作为图数据库节点ID
- `time/created_at/updated_at`: 系统设置当前时间戳
- `status`: 默认设置为"New"
- 枚举字段(`impact/risk/severity`): 转换为字符串值存储

### 5.2 证据处理流程

**流程概述**: 合并告警的所有证据制品为单个Evidence节点，提取风险实体并建立关联

**主要步骤**:
1. **证据合并**: 将多个evidence_artifacts合并为单个Evidence节点
2. **属性提取**: 分析证据内容，提取关键信息到动态属性
3. **实体提取**: 从证据中识别和提取各类风险实体
4. **去重处理**: 对提取的实体执行匹配和去重
5. **关系建立**: 创建Evidence与实体的RELATES_TO关系

**证据节点字段设置**:
- `evidence_id`: 基于证据内容生成UUID5确保唯一性
- `evidence_type`: 固定为"combined_evidence"
- `evidence_types`: 包含的所有证据类型列表
- `is_alert_trigger/include_payload`: 分析证据特征设置布尔标志
- `evidence_count`: 合并的证据制品数量
- `raw_evidence_data`: 完整的原始证据数据JSON

**动态属性提取**:
- 扫描所有证据制品，提取HTTP方法、域名、IP地址、文件哈希、进程名等关键信息
- 去重后存储为列表形式，便于后续查询和分析

**实体提取策略**:
- **HTTP请求/响应**: 从HTTP相关证据中提取请求和响应实体
- **网络端点**: 从src_endpoint/dst_endpoint中提取IP和端口信息
- **文件**: 从文件相关证据中提取文件哈希、路径等信息
- **进程**: 从进程相关证据中提取进程名、命令行等信息
- **URL/DNS**: 从URL和DNS查询中提取域名、路径等信息

### 5.3 实体去重流程

**流程概述**: 对从证据中提取的风险实体执行匹配算法，避免重复创建相同实体

**主要步骤**:
1. **匹配条件生成**: 根据实体类型和属性生成匹配条件
2. **现有实体查找**: 使用匹配条件查询数据库中的现有实体
3. **去重决策**: 如果找到匹配实体则复用，否则创建新实体
4. **复用更新**: 对复用的实体更新reuse_count和updated_at
5. **关系建立**: 建立证据与实体(新建或复用)的关联关系

**匹配策略**:
- **分层匹配**: 主要字段优先，辅助字段补充，备选字段兜底
- **类型特化**: 不同实体类型使用不同的匹配逻辑
- **容错处理**: 处理字段缺失和数据质量问题
- **查询设计**: 使用索引友好的查询条件

**实体创建字段设置**:
- `entity_id`: 系统生成UUID
- `reuse_count`: 初始值为1，每次复用时递增
- `risk_score`: 基于实体特征计算，复用时取历史最高值
- 特殊字段处理: 如ProcessEntity的process_signature、HttpResponseEntity的body_hash

### 5.4 设备关联流程

**流程概述**: 从告警数据中提取设备信息，创建或更新Device节点并建立关联

**主要步骤**:
1. **设备信息提取**: 从告警的product_name等字段提取设备相关信息
2. **设备ID生成**: 基于hostname > ip > product_name的优先级生成设备ID
3. **设备去重**: 检查是否已存在相同设备，存在则更新计数
4. **设备创建**: 不存在则创建新的Device节点
5. **关系建立**: 创建AlertDetail与Device的GENERATED_BY关系

**设备字段更新**:
- 新设备: 设置所有提取的字段，count初始化为1
- 现有设备: count递增，updated_at更新，其他字段保持不变

## 6. 实体去重机制

### 6.1 设计原则

**核心理念**: 通过基于规则的匹配算法识别相同实体，避免重复创建

**设计原则**:
- **分层匹配**: 主要字段优先，辅助字段补充，备选字段兜底
- **跨设备兼容**: 适配不同安全设备的数据格式差异
- **容错处理**: 处理字段缺失和数据质量问题
- **查询设计**: 使用索引友好的查询条件

### 6.2 匹配策略

#### 6.2.1 通用匹配流程
1. **条件生成**: 根据实体类型和属性生成分层匹配条件
2. **数据库查询**: 使用匹配条件查询现有实体
3. **结果处理**: 找到则复用并更新统计信息，否则创建新实体

#### 6.2.2 实体类型匹配策略

**NetworkEndpointEntity**:
- 主要匹配: `ip_address` + `port`
- 辅助匹配: `ip_address` + `endpoint_type`
- 备选匹配: 仅 `ip_address`

**FileEntity**:
- 主要匹配: `file_hash` (任何哈希算法)
- 辅助匹配: `filename` + `file_size`
- 备选匹配: 仅 `filename` 或 `file_path`

**ProcessEntity**:
- 主要匹配: `process_signature` (进程名+命令行的MD5哈希)
- 辅助匹配: `process_name` + `process_path`
- 备选匹配: 仅 `process_name`

**HttpRequestEntity**:
- 主要匹配: `method` + `url` + `user_agent`
- 辅助匹配: `method` + `url`
- 备选匹配: 仅 `url`

**UrlEntity**:
- 主要匹配: `full_url`
- 辅助匹配: `hostname` + `path`
- 备选匹配: 仅 `hostname`

**DnsQueryEntity**:
- 主要匹配: `query_name` + `query_type`
- 辅助匹配: 仅 `query_name`
- 备选匹配: `hostname` (兼容性)

**HttpResponseEntity**:
- 主要匹配: `status_code` + `content_type` + `body_hash`
- 辅助匹配: `status_code` + `content_type`
- 备选匹配: 仅 `status_code`

### 6.3 特殊字段处理

#### 6.3.1 系统生成字段
**ProcessEntity.process_signature**:
- 生成规则: MD5(`process_name|command_line`)
- 用途: 提供进程的匹配标识

**HttpResponseEntity.body_hash**:
- 生成规则: MD5(响应体内容)
- 用途: 避免大文本响应体的直接比较

#### 6.3.2 容错处理
**数据类型处理**:
- NULL值: 使用 `IS NULL` 条件匹配
- 空字符串: 与NULL等价处理
- 数值类型: 直接等值匹配
- 字符串类型: 去除空白后匹配

**查询处理**:
- 按更新时间排序获取最新实体
- 使用索引友好的查询条件
- 限制返回结果数量

### 6.4 复用统计和查询处理

#### 6.4.1 复用统计信息
每个实体维护复用相关统计:
- `reuse_count`: 实体被复用次数 (初始为1)
- `created_at`: 首次创建时间
- `updated_at`: 最后更新时间
- `risk_score`: 历史最高风险分数

#### 6.4.2 查询处理
**数据库索引**: 为关键匹配字段建立复合索引，支持分层匹配查询
**查询处理**: 按更新时间排序获取最新实体，限制返回结果数量
**特殊处理**: HTTP响应实体使用body_hash避免大文本比较

## 7. 实现状态总结

### 7.1 已完成功能

**核心节点类型** ✅:
- AlertDetail: 告警细节存储，包含完整属性和验证
- Verdict: 研判记录，支持AI和人工研判
- Evidence: 证据信息，支持多种证据类型合并
- Device: 设备信息，支持设备提取和去重
- 风险实体: 7种实体类型，支持去重

**关系类型** ✅:
- HAS_EVIDENCE: AlertDetail -> Evidence，包含触发和载荷信息
- RELATES_TO: Evidence -> Entity，包含实体上下文快照
- GENERATED_BY: AlertDetail -> Device，包含生成原因
- HAS_VERDICT: AlertDetail -> Verdict，支持多次研判

**业务流程** ✅:
- 告警创建: 告警细节创建流程
- 证据处理: 提取和关联风险实体
- 设备关联: 提取设备信息并建立关联
- 研判管理: 支持告警研判记录的创建和查询

**实体去重机制** ✅:
- 分层匹配: 主要、辅助、备选匹配条件
- 跨设备兼容: 支持不同安全设备的数据格式差异
- 容错处理: 基本的数据类型和空值处理
- 特殊处理: HTTP响应body_hash、进程签名等特殊字段处理
- 查询设计: 索引友好的查询设计和排序

### 7.2 系统特色

**实体去重机制**: 通过分层匹配算法避免重复实体创建
**跨设备兼容**: 适配WAF、IDS、EDR等不同安全设备的数据格式
**数据处理**: 支持告警数据的写入和关联分析
**分层架构**: 基于Repository模式的分层架构，便于功能扩展


