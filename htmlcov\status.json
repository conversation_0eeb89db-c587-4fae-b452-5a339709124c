{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "700a3dea0dba095abedf965c51c56914", "files": {"z_5f5a17c013354698___init___py": {"hash": "167885bc157b958b30e0e16f49cb7c67", "index": {"url": "z_5f5a17c013354698___init___py.html", "file": "app\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b___init___py": {"hash": "d74aaf8a67c03c858c9f8ee0295173b9", "index": {"url": "z_4a9cca768ff3c21b___init___py.html", "file": "app\\api\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_dependencies_py": {"hash": "71d3bb4835bdcb95ebad7be2d9d37a47", "index": {"url": "z_4a9cca768ff3c21b_dependencies_py.html", "file": "app\\api\\dependencies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dd8bbcc83a1ab31___init___py": {"hash": "c921227c80934664f5de6d6e1398bbf6", "index": {"url": "z_2dd8bbcc83a1ab31___init___py.html", "file": "app\\api\\routes\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dd8bbcc83a1ab31_alerts_py": {"hash": "f2433fe77d23a9771c5a0baf231440f3", "index": {"url": "z_2dd8bbcc83a1ab31_alerts_py.html", "file": "app\\api\\routes\\alerts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 81, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dd8bbcc83a1ab31_devices_py": {"hash": "96c45259b6eafbe7ff2721894a1c7533", "index": {"url": "z_2dd8bbcc83a1ab31_devices_py.html", "file": "app\\api\\routes\\devices.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dd8bbcc83a1ab31_entities_py": {"hash": "9d21faf0cd7ed1144ade927791a1dea6", "index": {"url": "z_2dd8bbcc83a1ab31_entities_py.html", "file": "app\\api\\routes\\entities.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dd8bbcc83a1ab31_evidences_py": {"hash": "135f32981d68f038d702f1dea82f9e1d", "index": {"url": "z_2dd8bbcc83a1ab31_evidences_py.html", "file": "app\\api\\routes\\evidences.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dd8bbcc83a1ab31_system_py": {"hash": "ff8a1beb941260d015dbef7a799dd9f8", "index": {"url": "z_2dd8bbcc83a1ab31_system_py.html", "file": "app\\api\\routes\\system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 176, "n_excluded": 0, "n_missing": 176, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dd8bbcc83a1ab31_verdicts_py": {"hash": "654138077cd42ed3d90a22ea4ee2e27a", "index": {"url": "z_2dd8bbcc83a1ab31_verdicts_py.html", "file": "app\\api\\routes\\verdicts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2___init___py": {"hash": "00949c1c87ad08f62fcf0d2073aabae5", "index": {"url": "z_adebc8a9b0574ea2___init___py.html", "file": "app\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_config_py": {"hash": "d532a35e6f621bcad26c7807f271c51e", "index": {"url": "z_adebc8a9b0574ea2_config_py.html", "file": "app\\core\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 46, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_constants_py": {"hash": "951637242f54d49223c32e2c88f5efcc", "index": {"url": "z_adebc8a9b0574ea2_constants_py.html", "file": "app\\core\\constants.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_de0c988eb77bc928___init___py": {"hash": "adbd8ab8809d4e2b4d9878020adb5468", "index": {"url": "z_de0c988eb77bc928___init___py.html", "file": "app\\database\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_de0c988eb77bc928_connection_py": {"hash": "68064682cff28e9995eb7ef45cc91c1a", "index": {"url": "z_de0c988eb77bc928_connection_py.html", "file": "app\\database\\connection.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "d808fd176bdca7dc25200c834597966b", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d___init___py": {"hash": "58af3db140563d51468126967c44427d", "index": {"url": "z_1374716a89f3e08d___init___py.html", "file": "app\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_alert_py": {"hash": "f0352d988ff72c075e7b5b5cf713d5eb", "index": {"url": "z_1374716a89f3e08d_alert_py.html", "file": "app\\models\\alert.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 233, "n_excluded": 0, "n_missing": 233, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_base_py": {"hash": "ab85094b665d0c230296d9ae024b2d72", "index": {"url": "z_1374716a89f3e08d_base_py.html", "file": "app\\models\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_device_py": {"hash": "dc236e29cf6212b4abbe6168ea1203a2", "index": {"url": "z_1374716a89f3e08d_device_py.html", "file": "app\\models\\device.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_entity_py": {"hash": "9428b2b65ac63e97fbc16b66e175d1dd", "index": {"url": "z_1374716a89f3e08d_entity_py.html", "file": "app\\models\\entity.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_verdict_py": {"hash": "0f4b3d2754f2bdd17ec9c2fa03ef9186", "index": {"url": "z_1374716a89f3e08d_verdict_py.html", "file": "app\\models\\verdict.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f50d20dc3c7ccdf___init___py": {"hash": "7e05f872830f047608e71876525a1e7f", "index": {"url": "z_6f50d20dc3c7ccdf___init___py.html", "file": "app\\repositories\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6f50d20dc3c7ccdf_base_py": {"hash": "a30d1e0e76b74a80dae1e0dd60b6c48e", "index": {"url": "z_6f50d20dc3c7ccdf_base_py.html", "file": "app\\repositories\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 169, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70___init___py": {"hash": "1d2587fab163b81e7f61c3e99d4eee8f", "index": {"url": "z_4c37ce8615b5aa70___init___py.html", "file": "app\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_alert_service_py": {"hash": "7fc0f5874de37e4321287c0d7f9eabd0", "index": {"url": "z_4c37ce8615b5aa70_alert_service_py.html", "file": "app\\services\\alert_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 140, "n_excluded": 0, "n_missing": 140, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_device_service_py": {"hash": "92d313a28c28ed6d19cb8eecbad68284", "index": {"url": "z_4c37ce8615b5aa70_device_service_py.html", "file": "app\\services\\device_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_entity_service_py": {"hash": "aa0dadf7040d4973d3bccd23c1ed2cf6", "index": {"url": "z_4c37ce8615b5aa70_entity_service_py.html", "file": "app\\services\\entity_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 382, "n_excluded": 0, "n_missing": 382, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_evidence_service_py": {"hash": "7784b0ce92f496a3c1274b74cd03a25a", "index": {"url": "z_4c37ce8615b5aa70_evidence_service_py.html", "file": "app\\services\\evidence_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 385, "n_excluded": 0, "n_missing": 385, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_verdict_service_py": {"hash": "e9b640167c5883504d6474a379fca94a", "index": {"url": "z_4c37ce8615b5aa70_verdict_service_py.html", "file": "app\\services\\verdict_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 114, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1___init___py": {"hash": "0ca89dd8620789f54eb4ce290da90ad7", "index": {"url": "z_a7b07432402c05f1___init___py.html", "file": "app\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_logger_py": {"hash": "e150e9ff4a57e2512ded5d8ba9995340", "index": {"url": "z_a7b07432402c05f1_logger_py.html", "file": "app\\utils\\logger.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_tag_utils_py": {"hash": "ae02062b49af6d3e43df09b8142924a3", "index": {"url": "z_a7b07432402c05f1_tag_utils_py.html", "file": "app\\utils\\tag_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}