# AlertGraph API 文档

## 概述

AlertGraph 是基于 Neo4j 的安全告警知识图谱系统，提供 RESTful API 服务用于告警数据管理、研判记录管理和系统监控。

**基础信息**:
- **API 版本**: v1
- **基础路径**: `/api/v1`
- **技术栈**: FastAPI + Neo4j
- **认证方式**: 暂无（开发阶段）

## API 设计原则

1. **RESTful 风格**: 遵循 REST 设计原则
2. **统一响应格式**: 所有 API 使用一致的响应结构
3. **明确的错误处理**: 使用标准 HTTP 状态码和详细错误信息
4. **参数验证**: 严格的输入参数验证
5. **资源导向**: 以资源为中心的 URL 设计

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-12-09T15:30:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error_code": "VALIDATION_ERROR",
  "message": "数据验证失败",
  "details": "具体错误信息",
  "timestamp": "2024-12-09T15:30:00Z"
}
```

## HTTP 状态码

| 状态码 | 说明 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | 请求成功处理 |
| 201 | 已创建 | 资源创建成功 |
| 400 | 请求错误 | 请求格式错误 |
| 404 | 未找到 | 资源不存在 |
| 422 | 数据验证失败 | 输入数据不符合要求 |
| 500 | 服务器错误 | 内部服务器错误 |

---

## 1. 告警管理 API

### 1.1 创建告警

**接口**: `POST /api/v1/alerts`

**描述**: 创建单个告警细节记录

**请求体**:
```json
{
  "alert_detail_id": "AD-20241209-001",
  "alert_name": "恶意文件检测",
  "alert_message": "检测到恶意文件执行",
  "product_name": "EDR-System",
  "confidence_score": 85,
  "impact": "High",
  "risk": "High", 
  "severity": "Critical",
  "status": "New",
  "vulnerabilities": "CVE-2024-1234",
  "comment": "需要进一步分析",
  "evidence_artifacts": [
    {
      "id": "evidence_001",
      "type": "file_detection",
      "is_alert_trigger": true,
      "file": {
        "name": "malware.exe",
        "path": "C:\\temp\\malware.exe",
        "hashes": [
          {
            "algorithm": "SHA256",
            "value": "abc123..."
          }
        ]
      }
    }
  ]
}
```

**响应**: `AlertDetailResponse`

### 1.2 批量创建告警

**接口**: `POST /api/v1/alerts/batch`

**描述**: 批量创建告警细节记录（最多1000条）

**请求体**: `AlertDetailCreate[]`

**响应**:
```json
{
  "success_count": 95,
  "failed_count": 5,
  "created_ids": ["alert_id_1", "alert_id_2", ...],
  "errors": [
    {
      "index": 3,
      "alert_detail_id": "AD-20241209-004",
      "error": "数据验证失败"
    }
  ]
}
```

### 1.3 获取告警详情

**接口**: `GET /api/v1/alerts/{alert_id}`

**描述**: 根据告警ID获取告警详细信息

**路径参数**:
- `alert_id`: 告警唯一标识符

**响应**: `AlertDetailResponse`

### 1.4 更新告警

**接口**: `PUT /api/v1/alerts/{alert_id}`

**描述**: 更新告警信息（包括状态更新）

**请求体**:
```json
{
  "alert_name": "更新后的告警名称",
  "status": "InProgress",
  "comment": "正在处理中"
}
```

**响应**: `AlertDetailResponse`

### 1.5 查询告警列表

**接口**: `GET /api/v1/alerts`

**描述**: 按时间范围查询告警列表

**查询参数**:
- `start_time`: 开始时间（必填）
- `end_time`: 结束时间（必填）

**响应**: `AlertDetailResponse[]`

### 1.6 获取设备告警

**接口**: `GET /api/v1/alerts/device/{device_id}`

**描述**: 获取指定设备的告警列表

**路径参数**:
- `device_id`: 设备唯一标识符

**响应**: `AlertDetailResponse[]`

### 1.7 获取告警关联实体

**接口**: `GET /api/v1/alerts/{alert_id}/entities`

**描述**: 获取告警相关的风险实体信息

**响应**:
```json
{
  "alert_id": "alert_123",
  "entities": [
    {
      "entity_id": "entity_001",
      "entity_type": "FileEntity",
      "name": "malware.exe",
      "risk_score": 90
    }
  ],
  "total_count": 1
}
```

---

## 2. 研判管理 API

### 2.1 创建研判记录

**接口**: `POST /api/v1/verdicts`

**描述**: 创建独立的研判记录

**请求体**:
```json
{
  "type_id": 2,
  "type": "人工研判",
  "label": 1,
  "reason": "经过分析，此告警为系统正常行为触发的误报",
  "commiter": "analyst_001",
  "comment": "建议调整检测规则阈值"
}
```

**响应**: `VerdictResponse`

### 2.2 获取研判详情

**接口**: `GET /api/v1/verdicts/{verdict_id}`

**描述**: 根据研判ID获取研判详细信息

**响应**: `VerdictResponse`

### 2.3 更新研判记录

**接口**: `PUT /api/v1/verdicts/{verdict_id}`

**描述**: 更新研判记录信息

**响应**: `VerdictResponse`

### 2.4 删除研判记录

**接口**: `DELETE /api/v1/verdicts/{verdict_id}`

**描述**: 删除指定的研判记录

**响应**: 204 No Content

### 2.5 为告警添加研判

**接口**: `POST /api/v1/alerts/{alert_id}/verdicts`

**描述**: 为指定告警添加研判记录

**请求体**: `VerdictCreate`

**响应**: `VerdictResponse`

### 2.6 获取告警研判列表

**接口**: `GET /api/v1/alerts/{alert_id}/verdicts`

**描述**: 获取告警的所有研判记录

**响应**: `VerdictResponse[]`

### 2.7 获取最新研判

**接口**: `GET /api/v1/alerts/{alert_id}/verdicts/latest`

**描述**: 获取告警的最新研判记录

**响应**: `VerdictResponse`

---

## 3. 系统管理 API

### 3.1 系统健康检查

**接口**: `GET /api/v1/system/health`

**描述**: 获取系统整体健康状态

**响应**:
```json
{
  "system": "AlertGraph",
  "status": "healthy",
  "timestamp": "2024-12-09T15:30:00Z",
  "database": {
    "status": "healthy",
    "response_time": 50
  },
  "version": "0.1.0"
}
```

### 3.2 系统统计信息

**接口**: `GET /api/v1/system/stats`

**描述**: 获取系统统计信息

**响应**:
```json
{
  "alert_count": 1000,
  "device_count": 50,
  "entity_count": 2000,
  "evidence_count": 800,
  "total_nodes": 3850,
  "timestamp": "2024-12-09T15:30:00Z"
}
```

### 3.3 数据库状态

**接口**: `GET /api/v1/system/database/status`

**描述**: 获取数据库连接和状态信息

### 3.4 节点统计

**接口**: `GET /api/v1/system/database/nodes`

**描述**: 获取图数据库节点统计信息

**查询参数**:
- `label`: 可选，指定节点标签

### 3.5 关系统计

**接口**: `GET /api/v1/system/database/relationships`

**描述**: 获取图数据库关系统计信息

**查询参数**:
- `rel_type`: 可选，指定关系类型

### 3.6 测试环境管理 (仅测试环境)

#### 3.6.1 重置测试数据库

**接口**: `POST /api/v1/system/test/reset-database`

**描述**: 重置测试数据库，删除所有数据

**环境要求**: 需要设置环境变量 `TESTING=true`

#### 3.6.2 清理测试数据

**接口**: `DELETE /api/v1/system/test/clean`

**描述**: 清理测试数据

**查询参数**:
- `labels`: 可选，要清理的节点标签列表
- `test_markers`: 可选，测试标识符列表

**请求头**:
- `X-Testing: true`: 标识测试环境

#### 3.6.3 获取测试环境状态

**接口**: `GET /api/v1/system/test/status`

**描述**: 获取测试环境状态信息

### 3.7 日志和性能监控

#### 3.7.1 获取最近日志

**接口**: `GET /api/v1/system/logs/recent`

**查询参数**:
- `limit`: 日志条数限制 (1-1000，默认100)
- `level`: 可选，日志级别过滤

#### 3.7.2 获取性能指标

**接口**: `GET /api/v1/system/performance/metrics`

**描述**: 获取系统性能指标

**响应**:
```json
{
  "metrics": {
    "api_response_time": {
      "average": 150,
      "p95": 300,
      "p99": 500
    },
    "database_query_time": {
      "average": 50,
      "p95": 100,
      "p99": 200
    },
    "memory_usage": {
      "used": 512,
      "total": 2048,
      "percentage": 25
    },
    "request_count": {
      "total": 1000,
      "successful": 950,
      "failed": 50
    }
  },
  "timestamp": "2024-12-09T15:30:00Z",
  "collection_interval": "1m"
}
```

---

## 4. 数据模型

### 4.1 告警细节模型 (AlertDetailResponse)

```json
{
  "vid": "alert_uuid",
  "alert_detail_id": "AD-20241209-001",
  "alert_name": "恶意文件检测",
  "alert_message": "检测到恶意文件执行",
  "product_name": "EDR-System",
  "confidence_score": 85,
  "impact": "High",
  "risk": "High",
  "severity": "Critical", 
  "status": "New",
  "vulnerabilities": "CVE-2024-1234",
  "comment": "需要进一步分析",
  "time": "2024-12-09T15:30:00Z",
  "created_at": "2024-12-09T15:30:00Z",
  "updated_at": "2024-12-09T15:30:00Z",
  "data_tag": "production"
}
```

### 4.2 研判记录模型 (VerdictResponse)

```json
{
  "verdict_id": "VD-20241209-001",
  "type_id": 2,
  "type": "人工研判",
  "label": 1,
  "reason": "经过分析，此告警为系统正常行为触发的误报",
  "commiter": "analyst_001",
  "comment": "建议调整检测规则阈值",
  "time": "2024-12-09T15:30:00Z",
  "created_at": "2024-12-09T15:30:00Z",
  "updated_at": "2024-12-09T15:30:00Z"
}
```

---

## 5. 枚举值定义

### 5.1 告警状态 (AlertStatus)
- `New`: 新建
- `InProgress`: 处理中
- `Suppressed`: 已抑制
- `Resolved`: 已解决
- `Archived`: 已归档

### 5.2 严重性等级 (Severity)
- `Fatal`: 致命
- `Critical`: 严重
- `High`: 高
- `Medium`: 中等
- `Low`: 低

### 5.3 影响等级 (Impact)
- `Critical`: 严重
- `High`: 高
- `Medium`: 中等
- `Low`: 低

### 5.4 风险等级 (Risk)
- `Critical`: 严重
- `High`: 高
- `Medium`: 中等
- `Low`: 低

### 5.5 研判类型 (VerdictType)
- `1`: AI研判
- `2`: 人工研判

### 5.6 研判标签 (VerdictLabel)
- `1`: 误报
- `2`: 确认攻击
- `3`: 可疑
- `4`: 数据不足

---

## 6. 错误处理

### 6.1 常见错误码

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| VALIDATION_ERROR | 422 | 数据验证失败 |
| NOT_FOUND | 404 | 资源不存在 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |
| BAD_REQUEST | 400 | 请求格式错误 |

### 6.2 错误响应示例

```json
{
  "success": false,
  "error_code": "VALIDATION_ERROR",
  "message": "数据验证失败: 开始时间和结束时间都是必需的",
  "details": "start_time and end_time are required",
  "timestamp": "2024-12-09T15:30:00Z"
}
```

---

## 7. 使用示例

### 7.1 创建告警并添加研判

```bash
# 1. 创建告警
curl -X POST "http://localhost:8000/api/v1/alerts" \
  -H "Content-Type: application/json" \
  -d '{
    "alert_detail_id": "AD-20241209-001",
    "alert_name": "恶意文件检测",
    "alert_message": "检测到恶意文件执行",
    "product_name": "EDR-System",
    "confidence_score": 85,
    "impact": "High",
    "risk": "High",
    "severity": "Critical"
  }'

# 2. 为告警添加研判
curl -X POST "http://localhost:8000/api/v1/alerts/{alert_id}/verdicts" \
  -H "Content-Type: application/json" \
  -d '{
    "type_id": 2,
    "type": "人工研判",
    "label": 1,
    "reason": "经过分析，此告警为误报",
    "commiter": "analyst_001"
  }'
```

### 7.2 查询告警列表

```bash
curl -X GET "http://localhost:8000/api/v1/alerts?start_time=2024-12-09T00:00:00Z&end_time=2024-12-09T23:59:59Z"
```

### 7.3 获取系统状态

```bash
curl -X GET "http://localhost:8000/api/v1/system/health"
curl -X GET "http://localhost:8000/api/v1/system/stats"
```

---

## 8. API 使用最佳实践

### 8.1 性能优化建议

1. **批量操作**: 对于大量告警数据，使用批量创建接口而非单个创建
2. **时间范围查询**: 查询告警时指定合理的时间范围，避免全量查询
3. **分页处理**: 对于大量数据的查询，考虑实现分页机制
4. **缓存策略**: 对于频繁查询的数据，建议在客户端实现适当的缓存

### 8.2 错误处理建议

1. **重试机制**: 对于5xx错误，建议实现指数退避的重试机制
2. **超时设置**: 设置合理的请求超时时间（建议30秒）
3. **错误日志**: 记录详细的错误信息用于问题排查
4. **降级策略**: 在系统不可用时，考虑实现降级策略

### 8.3 数据一致性

1. **幂等性**: 创建操作使用唯一的`alert_detail_id`确保幂等性
2. **事务处理**: 批量操作中的部分失败不会影响已成功的操作
3. **数据验证**: 在客户端进行基础数据验证，减少网络请求

### 8.4 安全考虑

1. **输入验证**: 严格验证所有输入参数，防止注入攻击
2. **访问控制**: 生产环境中应实现适当的认证和授权机制
3. **敏感数据**: 避免在日志中记录敏感信息
4. **测试环境**: 测试相关的API仅在测试环境中使用

---

## 9. 故障排查

### 9.1 常见问题

#### 问题1: 告警创建失败
**症状**: POST /api/v1/alerts 返回422错误
**可能原因**:
- 必填字段缺失
- 枚举值不正确
- 数据格式错误

**解决方案**:
1. 检查请求体中的必填字段
2. 验证枚举值是否正确
3. 确认数据类型匹配

#### 问题2: 数据库连接失败
**症状**: 系统健康检查返回数据库不健康
**可能原因**:
- Neo4j服务未启动
- 连接配置错误
- 网络连接问题

**解决方案**:
1. 检查Neo4j服务状态
2. 验证连接配置
3. 测试网络连通性

#### 问题3: 批量操作部分失败
**症状**: 批量创建返回部分成功
**可能原因**:
- 部分数据格式错误
- 重复的alert_detail_id
- 数据库约束冲突

**解决方案**:
1. 检查errors字段中的具体错误信息
2. 验证数据唯一性
3. 重新提交失败的数据

### 9.2 监控指标

建议监控以下关键指标：
- API响应时间
- 错误率
- 数据库连接状态
- 系统资源使用率
- 告警处理量

---

## 10. 版本历史

### v1.0.0 (2024-12-09)
- 初始版本发布
- 实现告警管理、研判管理、系统管理三大模块
- 删除批量研判功能，简化API结构
- 移除实体管理API（实体由证据自动提取）
- 统一错误处理和响应格式
- 优化API命名规范

#### API优化内容
1. **删除冗余功能**:
   - 移除批量研判API (`POST /verdicts/batch`, `POST /alerts/batch/verdict`)
   - 移除相似告警研判API (`POST /alerts/{id}/verdicts/similar`)
   - 移除实体管理API (entities相关的所有端点)

2. **统一错误处理**:
   - 使用标准HTTP状态码 (422用于验证错误，404用于资源不存在)
   - 统一错误响应格式
   - 添加详细的错误信息

3. **API风格统一**:
   - 删除独立的状态更新接口，合并到通用更新接口
   - 统一命名规范和响应格式
   - 优化参数验证逻辑

4. **功能简化**:
   - 保留告警批量创建功能（业务需要）
   - 简化研判管理，专注于单个研判记录操作
   - 实体信息通过告警关联查询获取

---

## 11. 联系信息

- **项目**: AlertGraph
- **版本**: v0.1.0
- **文档更新**: 2025-06-11
- **API文档**: http://localhost:8000/docs
- **开发环境**: http://localhost:8000
- **健康检查**: http://localhost:8000/api/v1/system/health
