"""
FastAPI应用主文件

创建和配置FastAPI应用实例
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import time
import logging
import uvicorn

from app.core.config import settings
from app.api import api_router
from app.utils.logger import logger


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时预热组件
    try:
        from app.database.connection import neo4j_connection, ensure_connection_ready
        from app.api.dependencies import get_alert_service, get_entity_service, get_verdict_service
        
        ensure_connection_ready()
        
        # 预热服务依赖
        get_alert_service(neo4j_connection)
        get_entity_service(neo4j_connection) 
        get_verdict_service(neo4j_connection)
        
        logger.info("服务已启动...")
    except Exception as e:
        logger.error(f"❌ 应用组件预热失败: {e}")
    
    yield
    
    # 关闭时清理资源（如果需要）


def create_app() -> FastAPI:
    """创建并配置FastAPI应用"""

    # 配置统一日志
    configure_logging()

    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="基于Neo4j的安全告警知识图谱系统API",
        docs_url=settings.docs_url,
        redoc_url=settings.redoc_url,
        debug=settings.debug,
        lifespan=lifespan,
    )

    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境中应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 添加API路由
    app.include_router(api_router, prefix=settings.api_prefix)

    # 添加根路径和健康检查路由
    @app.get("/")
    async def root():
        """根路径，返回API基本信息"""
        return {
            "name": settings.app_name,
            "version": settings.app_version,
            "description": "基于Neo4j的安全告警知识图谱系统",
            "docs_url": settings.docs_url,
            "api_prefix": settings.api_prefix,
        }

    @app.get("/health")
    async def health_check():
        """应用健康检查接口"""
        return {"status": "healthy", "service": settings.app_name}

    @app.get("/health/database")
    async def database_health_check():
        """数据库健康检查接口"""
        from app.database.connection import neo4j_connection

        try:
            health_result = neo4j_connection.health_check()
            return {
                "service": "database",
                "timestamp": health_result.get("timestamp"),
                **health_result,
            }
        except Exception as e:
            return {"service": "database", "status": "unhealthy", "error": str(e)}

    # 添加API请求日志中间件
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        start_time = time.time()

        # 记录请求（已在logger中做去重处理）
        logger.api_request(
            method=request.method,
            path=request.url.path,
            client_ip=request.client.host if request.client else "unknown",
        )

        # 处理请求
        response = await call_next(request)

        # 记录响应（已在logger中做筛选处理）
        duration = time.time() - start_time
        logger.api_response(
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            duration=duration,
        )

        return response

    return app


def configure_logging():
    """配置统一日志输出"""
    # 禁用uvicorn的重复日志
    uvicorn_access = logging.getLogger("uvicorn.access")
    uvicorn_access.disabled = True  # 直接禁用，避免重复

    # 只保留错误日志
    uvicorn_error = logging.getLogger("uvicorn.error")
    uvicorn_error.setLevel(logging.ERROR)

    logger.info("应用启动，日志系统初始化完成")


# 创建应用实例
app = create_app()


def start_server():
    """启动API服务器"""
    import uvicorn

    logger.info("🚀 启动AlertGraph API服务器")
    logger.info(f"环境: {settings.environment}, 调试模式: {settings.debug}")

    # 启动服务器
    uvicorn.run(
        "app.main:app",  # 使用字符串引用避免循环导入
        host="0.0.0.0",
        port=8000,
        log_config=None,  # 禁用默认日志配置
        access_log=False,  # 禁用访问日志（我们有自己的中间件）
        reload=settings.debug,  # 开发模式下启用热重载
    )


if __name__ == "__main__":
    start_server()
