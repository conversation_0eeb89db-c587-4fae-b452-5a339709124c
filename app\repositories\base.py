"""
基础仓储类

封装对Neo4j的CRUD操作，提供面向对象的数据访问接口
"""

import logging
from typing import List, Dict, Any, Optional, Union
from uuid import uuid4
from datetime import datetime, UTC

from app.database.connection import Neo4jConnection
from app.core.constants import NodeLabels, RelationshipTypes
from app.utils.logger import logger as app_logger

logger = logging.getLogger(__name__)


class BaseRepository:
    """基础仓储类，提供通用CRUD操作"""

    def __init__(self, connection: Neo4jConnection):
        """
        初始化仓储

        Args:
            connection: Neo4j连接实例
        """
        self.connection = connection

    def create_node(self, label: str, properties: Dict[str, Any]) -> str:
        """
        创建节点

        Args:
            label: 节点标签
            properties: 节点属性

        Returns:
            创建的节点ID
        """
        # 系统生成ID（如果不存在）
        if "vid" not in properties:
            properties["vid"] = str(uuid4())

        # 添加创建时间
        if "created_time" not in properties:
            properties["created_time"] = datetime.now(UTC).isoformat()

        query = f"""
        CREATE (n:{label} $properties)
        RETURN n.vid as node_id
        """

        try:
            result = self.connection.execute_write_transaction(
                query, {"properties": properties}
            )
            if result:
                node_id = result[0]["node_id"]
                return node_id
            else:
                raise Exception("创建节点失败，未返回结果")
        except Exception as e:
            logger.error(
                f"创建节点失败 {label}",
                error=e,
                properties_keys=list(properties.keys()),
            )
            raise

    def get_node_by_id(
        self, node_id: str, label: str = None
    ) -> Optional[Dict[str, Any]]:
        """
        根据ID获取节点

        Args:
            node_id: 节点ID
            label: 节点标签（可选，用于查询处理）

        Returns:
            节点数据或None
        """
        if label:
            query = f"MATCH (n:{label} {{vid: $node_id}}) RETURN n"
        else:
            query = "MATCH (n {vid: $node_id}) RETURN n"

        try:
            result = self.connection.execute_read_transaction(
                query, {"node_id": node_id}
            )
            if result:
                node_data = dict(result[0]["n"])
                return node_data
            return None
        except Exception as e:
            logger.error(f"获取节点失败 {node_id}: {e}")
            raise

    def update_node(
        self, node_id: str, properties: Dict[str, Any], label: str = None
    ) -> bool:
        """
        更新节点属性

        Args:
            node_id: 节点ID
            properties: 要更新的属性
            label: 节点标签（可选）

        Returns:
            更新是否成功
        """
        # 添加更新时间
        properties["updated_time"] = datetime.now(UTC).isoformat()

        # 构建SET子句
        set_clauses = []
        for key in properties.keys():
            set_clauses.append(f"n.{key} = $properties.{key}")
        set_clause = ", ".join(set_clauses)

        if label:
            query = f"MATCH (n:{label} {{vid: $node_id}}) SET {set_clause} RETURN count(n) as updated"
        else:
            query = f"MATCH (n {{vid: $node_id}}) SET {set_clause} RETURN count(n) as updated"

        try:
            result = self.connection.execute_write_transaction(
                query, {"node_id": node_id, "properties": properties}
            )
            updated_count = result[0]["updated"] if result else 0
            success = updated_count > 0

            if success:
                logger.info(f"成功更新节点 {node_id}")
            else:
                logger.warning(f"节点更新失败，节点可能不存在: {node_id}")

            return success
        except Exception as e:
            logger.error(f"更新节点失败 {node_id}: {e}")
            raise

    def delete_node(self, node_id: str, label: str = None) -> bool:
        """
        删除节点

        Args:
            node_id: 节点ID
            label: 节点标签（可选）

        Returns:
            删除是否成功
        """
        if label:
            query = f"MATCH (n:{label} {{vid: $node_id}}) DETACH DELETE n RETURN count(n) as deleted"
        else:
            query = (
                "MATCH (n {vid: $node_id}) DETACH DELETE n RETURN count(n) as deleted"
            )

        try:
            result = self.connection.execute_write_transaction(
                query, {"node_id": node_id}
            )
            deleted_count = result[0]["deleted"] if result else 0
            success = deleted_count > 0

            if success:
                logger.info(f"成功删除节点 {node_id}")
            else:
                logger.warning(f"节点删除失败，节点可能不存在: {node_id}")

            return success
        except Exception as e:
            logger.error(f"删除节点失败 {node_id}: {e}")
            raise

    def create_relationship(
        self,
        from_id: str,
        to_id: str,
        rel_type: str,
        properties: Dict[str, Any] = None,
        from_label: str = None,
        to_label: str = None,
    ) -> bool:
        """
        创建关系

        Args:
            from_id: 起始节点ID
            to_id: 目标节点ID
            rel_type: 关系类型
            properties: 关系属性
            from_label: 起始节点标签（可选）
            to_label: 目标节点标签（可选）

        Returns:
            创建是否成功
        """
        properties = properties or {}
        properties["created_time"] = datetime.now(UTC).isoformat()

        # 根据节点标签确定正确的ID字段
        def get_id_field(label: str) -> str:
            """根据节点标签获取对应的ID字段"""
            id_field_mapping = {
                "AlertDetail": "vid",
                "Evidence": "evidence_id",
                "Device": "device_id",
                "NetworkEndpointEntity": "entity_id",
                "FileEntity": "entity_id",
                "ProcessEntity": "entity_id",
                "HttpRequestEntity": "entity_id",
                "HttpResponseEntity": "entity_id",
                "DnsQueryEntity": "entity_id",
                "UrlEntity": "entity_id",
            }
            return id_field_mapping.get(label, "vid")  # 默认使用vid

        # 构建MATCH子句，使用正确的ID字段
        if from_label:
            from_id_field = get_id_field(from_label)
            from_match = f"(from:{from_label} {{{from_id_field}: $from_id}})"
        else:
            # 如果没有标签，使用通用匹配（先尝试vid，然后尝试其他ID字段）
            from_match = """(from)
            WHERE from.vid = $from_id 
               OR from.evidence_id = $from_id 
               OR from.device_id = $from_id 
               OR from.entity_id = $from_id"""

        if to_label:
            to_id_field = get_id_field(to_label)
            to_match = f"(to:{to_label} {{{to_id_field}: $to_id}})"
        else:
            # 如果没有标签，使用通用匹配
            to_match = """(to)
            WHERE to.vid = $to_id 
               OR to.evidence_id = $to_id 
               OR to.device_id = $to_id 
               OR to.entity_id = $to_id"""

        query = f"""
        MATCH {from_match}, {to_match}
        CREATE (from)-[r:{rel_type} $properties]->(to)
        RETURN count(r) as created
        """

        try:
            result = self.connection.execute_write_transaction(
                query, {"from_id": from_id, "to_id": to_id, "properties": properties}
            )

            created_count = result[0]["created"] if result else 0
            success = created_count > 0

            # 只在失败时或debug模式下记录日志
            if not success:
                app_logger.warning(
                    f"关系创建失败: {from_id} -[{rel_type}]-> {to_id}",
                    from_id=from_id,
                    to_id=to_id,
                    rel_type=rel_type,
                    from_label=from_label,
                    to_label=to_label,
                    created_count=created_count,
                )
            elif app_logger.logger.isEnabledFor(logging.DEBUG):
                app_logger.debug(
                    f"成功创建关系 {from_id} -[{rel_type}]-> {to_id}",
                    from_id=from_id,
                    to_id=to_id,
                    rel_type=rel_type,
                )

            return success
        except Exception as e:
            app_logger.error(
                f"创建关系异常 {from_id} -[{rel_type}]-> {to_id}: {e}",
                from_id=from_id,
                to_id=to_id,
                rel_type=rel_type,
                from_label=from_label,
                to_label=to_label,
                error=str(e),
            )
            raise

    def batch_create_nodes(self, nodes: List[Dict[str, Any]]) -> List[str]:
        """
        批量创建节点

        Args:
            nodes: 节点列表，每个元素包含label和properties

        Returns:
            创建的节点ID列表
        """
        queries = []
        for node in nodes:
            label = node.get("label", "")
            properties = node.get("properties", {})

            # 系统生成ID
            if "vid" not in properties:
                properties["vid"] = str(uuid4())

            # 添加创建时间
            if "created_time" not in properties:
                properties["created_time"] = datetime.now(UTC).isoformat()

            query = f"CREATE (n:{label} $properties) RETURN n.vid as node_id"
            queries.append({"query": query, "parameters": {"properties": properties}})

        try:
            results = self.connection.execute_batch_write(queries)
            node_ids = []
            for result in results:
                if result:
                    node_ids.append(result[0]["node_id"])

            # 只在有显著数量时记录日志
            if len(node_ids) >= 10:
                logger.info(f"批量创建节点成功，数量: {len(node_ids)}")
            return node_ids
        except Exception as e:
            logger.error(f"批量创建节点失败", error=e, node_count=len(queries))
            raise

    def node_exists(self, node_id: str, label: str = None) -> bool:
        """
        检查节点是否存在

        Args:
            node_id: 节点ID
            label: 节点标签（可选）

        Returns:
            节点是否存在
        """
        if label:
            query = f"MATCH (n:{label} {{vid: $node_id}}) RETURN count(n) as count"
        else:
            query = "MATCH (n {vid: $node_id}) RETURN count(n) as count"

        try:
            result = self.connection.execute_read_transaction(
                query, {"node_id": node_id}
            )
            count = result[0]["count"] if result else 0
            return count > 0
        except Exception as e:
            logger.error(f"检查节点存在性失败 {node_id}: {e}")
            return False

    def get_nodes_by_label(
        self,
        label: str,
        limit: int = 100,
        skip: int = 0,
        where_clause: str = None,
        parameters: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """
        根据标签获取节点列表

        Args:
            label: 节点标签
            limit: 限制数量
            skip: 跳过数量
            where_clause: WHERE条件子句
            parameters: 查询参数

        Returns:
            节点列表
        """
        query = f"MATCH (n:{label})"

        if where_clause:
            query += f" WHERE {where_clause}"

        query += f" RETURN n ORDER BY n.created_time DESC SKIP {skip} LIMIT {limit}"

        try:
            result = self.connection.execute_read_transaction(query, parameters or {})
            nodes = [dict(record["n"]) for record in result]
            return nodes
        except Exception as e:
            logger.error(f"获取节点列表失败 {label}", error=e, query=query)
            raise

    def get_relationships(
        self,
        node_id: str,
        direction: str = "both",
        rel_type: str = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        获取节点的关系

        Args:
            node_id: 节点ID
            direction: 关系方向 (incoming/outgoing/both)
            rel_type: 关系类型（可选）
            limit: 限制数量

        Returns:
            关系列表
        """
        if direction == "outgoing":
            if rel_type:
                pattern = f"(n)-[r:{rel_type}]->(m)"
            else:
                pattern = "(n)-[r]->(m)"
        elif direction == "incoming":
            if rel_type:
                pattern = f"(n)<-[r:{rel_type}]-(m)"
            else:
                pattern = "(n)<-[r]-(m)"
        else:  # both
            if rel_type:
                pattern = f"(n)-[r:{rel_type}]-(m)"
            else:
                pattern = "(n)-[r]-(m)"

        query = f"""
        MATCH {pattern}
        WHERE n.vid = $node_id
        RETURN r, m, type(r) as rel_type
        LIMIT {limit}
        """

        try:
            result = self.connection.execute_read_transaction(
                query, {"node_id": node_id}
            )
            relationships = []
            for record in result:
                relationships.append(
                    {
                        "relationship": dict(record["r"]),
                        "related_node": dict(record["m"]),
                        "type": record["rel_type"],
                    }
                )

            return relationships
        except Exception as e:
            logger.error(
                f"获取节点关系失败 {node_id}",
                error=e,
                direction=direction,
                rel_type=rel_type,
            )
            raise
