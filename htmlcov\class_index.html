<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">0%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-11 14:56 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app\__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html">app\api\__init__.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html">app\api\dependencies.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31___init___py.html">app\api\routes\__init__.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>176</td>
                <td>176</td>
                <td>0</td>
                <td class="right" data-ratio="0 176">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html">app\core\__init__.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t12">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t12"><data value='Settings'>Settings</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t11">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t11"><data value='NodeLabels'>NodeLabels</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t27">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t27"><data value='RelationshipTypes'>RelationshipTypes</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t38">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t38"><data value='AlertImpact'>AlertImpact</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t47">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t47"><data value='AlertRisk'>AlertRisk</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t56">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t56"><data value='AlertSeverity'>AlertSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t66">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t66"><data value='AlertStatus'>AlertStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t77">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t77"><data value='VerdictType'>VerdictType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t83">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t83"><data value='VerdictLabel'>VerdictLabel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t92">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html#t92"><data value='ResponseStatus'>ResponseStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928___init___py.html">app\database\__init__.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t18">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t18"><data value='Neo4jConnection'>Neo4jConnection</data></a></td>
                <td>85</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="0 85">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html">app\models\__init__.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t24">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t24"><data value='HttpHeaderModel'>HttpHeaderModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t30">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t30"><data value='UrlModel'>UrlModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t44">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t44"><data value='HttpRequestModel'>HttpRequestModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t60">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t60"><data value='HttpResponseModel'>HttpResponseModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t72">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t72"><data value='DnsQueryModel'>DnsQueryModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t82">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t82"><data value='AutonomousSystemModel'>AutonomousSystemModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t88">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t88"><data value='GeoLocationModel'>GeoLocationModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t101">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t101"><data value='OperatingSystemModel'>OperatingSystemModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t107">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t107"><data value='NetworkEndpointModel'>NetworkEndpointModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t125">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t125"><data value='FingerprintModel'>FingerprintModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t132">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t132"><data value='FileModel'>FileModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t150">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t150"><data value='EnvironmentVariableModel'>EnvironmentVariableModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t156">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t156"><data value='UserModel'>UserModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t166">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t166"><data value='ProcessModel'>ProcessModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t181">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t181"><data value='EvidenceArtifactsModel'>EvidenceArtifactsModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t212">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t212"><data value='AlertDetailBase'>AlertDetailBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t229">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t229"><data value='AlertDetailCreate'>AlertDetailCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t235">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t235"><data value='AlertDetailUpdate'>AlertDetailUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t248">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t248"><data value='AlertDetailResponse'>AlertDetailResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t256">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t256"><data value='VerdictBase'>VerdictBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t267">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t267"><data value='VerdictCreate'>VerdictCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t272">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t272"><data value='VerdictUpdate'>VerdictUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t282">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t282"><data value='VerdictResponse'>VerdictResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t290">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t290"><data value='EvidenceBase'>EvidenceBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t296">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t296"><data value='EvidenceCreate'>EvidenceCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t301">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t301"><data value='EvidenceResponse'>EvidenceResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t309">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t309"><data value='DeviceBase'>DeviceBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t320">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t320"><data value='DeviceCreate'>DeviceCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t325">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t325"><data value='DeviceResponse'>DeviceResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t332">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t332"><data value='AlertSearchFilters'>AlertSearchFilters</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t353">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t353"><data value='BatchCreateResponse'>BatchCreateResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t361">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t361"><data value='BatchVerdictResponse'>BatchVerdictResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>230</td>
                <td>230</td>
                <td>0</td>
                <td class="right" data-ratio="0 230">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t13">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t13"><data value='TimestampedModel'>TimestampedModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t20">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t20"><data value='PaginationModel'>PaginationModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t27">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t27"><data value='ResponseModel'>ResponseModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t34">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t34"><data value='PaginatedResponse'>PaginatedResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t41">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t41"><data value='AlertStatus'>AlertStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t51">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t51"><data value='Severity'>Severity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t61">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t61"><data value='Impact'>Impact</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t70">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t70"><data value='Risk'>Risk</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t79">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t79"><data value='VerdictType'>VerdictType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t85">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t85"><data value='VerdictLabel'>VerdictLabel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t93">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t93"><data value='EntityType'>EntityType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t105">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html#t105"><data value='DataTag'>DataTag</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t12">app\models\device.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t12"><data value='DeviceBase'>DeviceBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t24">app\models\device.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t24"><data value='DeviceCreate'>DeviceCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t29">app\models\device.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t29"><data value='DeviceUpdate'>DeviceUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t40">app\models\device.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t40"><data value='DeviceResponse'>DeviceResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t45">app\models\device.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html#t45"><data value='Config'>DeviceResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html">app\models\device.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t14">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t14"><data value='EntityBase'>EntityBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t24">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t24"><data value='EntityCreate'>EntityCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t29">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t29"><data value='EntityUpdate'>EntityUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t37">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t37"><data value='EntityResponse'>EntityResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t44">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t44"><data value='RelationshipBase'>RelationshipBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t50">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t50"><data value='RelationshipCreate'>RelationshipCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t56">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t56"><data value='RelationshipResponse'>RelationshipResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t65">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t65"><data value='EntitySearchParams'>EntitySearchParams</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t72">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t72"><data value='PathResponse'>PathResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t81">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t81"><data value='HttpRequestEntity'>HttpRequestEntity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t91">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t91"><data value='HttpResponseEntity'>HttpResponseEntity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t99">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t99"><data value='DnsQueryEntity'>DnsQueryEntity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t107">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t107"><data value='NetworkEndpointEntity'>NetworkEndpointEntity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t116">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t116"><data value='UrlEntity'>UrlEntity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t125">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t125"><data value='FileEntity'>FileEntity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t136">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html#t136"><data value='ProcessEntity'>ProcessEntity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t13">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t13"><data value='VerdictType'>VerdictType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t19">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t19"><data value='VerdictLabel'>VerdictLabel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t27">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t27"><data value='VerdictCreate'>VerdictCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t35">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t35"><data value='Config'>VerdictCreate.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t48">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t48"><data value='VerdictUpdate'>VerdictUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t54">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t54"><data value='Config'>VerdictUpdate.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t58">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t58"><data value='VerdictResponse'>VerdictResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t76">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t76"><data value='Config'>VerdictResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t97">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t97"><data value='VerdictQuery'>VerdictQuery</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t110">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t110"><data value='Config'>VerdictQuery.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t114">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t114"><data value='VerdictListResponse'>VerdictListResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t123">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t123"><data value='VerdictStatistics'>VerdictStatistics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t132">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t132"><data value='Config'>VerdictStatistics.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf___init___py.html">app\repositories\__init__.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t19">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t19"><data value='BaseRepository'>BaseRepository</data></a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t27">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t27"><data value='AlertService'>AlertService</data></a></td>
                <td>116</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="0 116">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t20">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t20"><data value='DeviceService'>DeviceService</data></a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t27">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t27"><data value='EntityService'>EntityService</data></a></td>
                <td>347</td>
                <td>347</td>
                <td>0</td>
                <td class="right" data-ratio="0 347">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t23">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t23"><data value='EvidenceService'>EvidenceService</data></a></td>
                <td>342</td>
                <td>342</td>
                <td>0</td>
                <td class="right" data-ratio="0 342">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t21">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t21"><data value='VerdictService'>VerdictService</data></a></td>
                <td>97</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="0 97">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html">app\utils\__init__.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t25">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t25"><data value='JsonFormatter'>JsonFormatter</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t61">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t61"><data value='AlertGraphLogger'>AlertGraphLogger</data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t13">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t13"><data value='TagManager'>TagManager</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2774</td>
                <td>2774</td>
                <td>0</td>
                <td class="right" data-ratio="0 2774">0%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-11 14:56 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
