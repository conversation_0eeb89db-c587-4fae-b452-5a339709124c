"""
系统管理API

提供系统管理功能，如测试数据清理等
"""

import os
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Header
from app.database.connection import get_neo4j_connection, Neo4jConnection
from app.models.base import DataTag
from app.utils.tag_utils import TagManager
from app.utils.logger import logger

router = APIRouter()


@router.delete("/test/clean")
async def clean_test_data(
    test_markers: Optional[str] = None,
    data_tag: Optional[str] = None,
    x_testing: Optional[str] = Header(None),
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """
    清理测试数据
    
    Args:
        test_markers: 测试标识符，多个用逗号分隔
        data_tag: 数据标签过滤
        x_testing: 请求头标识，用于安全验证
    """
    # 安全检查：只允许在测试环境中调用
    if not x_testing or x_testing.lower() != "true":
        if not os.getenv("TESTING"):
            raise HTTPException(
                status_code=403, 
                detail="此API只能在测试环境中使用"
            )
    
    try:
        cleaned_results = {
            "cleaned_nodes": {},
            "cleaned_relationships": 0,
            "test_markers": [],
            "data_tags": []
        }
        
        # 方式1: 基于test_markers清理
        if test_markers:
            markers = [marker.strip() for marker in test_markers.split(",")]
            for marker in markers:
                if marker:
                    result = await _clean_by_test_marker(connection, marker)
                    cleaned_results["test_markers"].append(marker)
                    for node_type, count in result["nodes"].items():
                        cleaned_results["cleaned_nodes"][node_type] = \
                            cleaned_results["cleaned_nodes"].get(node_type, 0) + count
                    cleaned_results["cleaned_relationships"] += result["relationships"]
        
        # 方式2: 基于data_tag清理
        if data_tag:
            try:
                tag = DataTag(data_tag)
                result = await _clean_by_data_tag(connection, tag)
                cleaned_results["data_tags"].append(data_tag)
                for node_type, count in result["nodes"].items():
                    cleaned_results["cleaned_nodes"][node_type] = \
                        cleaned_results["cleaned_nodes"].get(node_type, 0) + count
                cleaned_results["cleaned_relationships"] += result["relationships"]
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的数据标签: {data_tag}")
        
        # 方式3: 通用测试数据清理（默认清理testing标签）
        if not test_markers and not data_tag:
            # 自动检测并清理测试相关标签
            testing_tags = [DataTag.TESTING, DataTag.DEVELOPMENT, DataTag.UNIT_TEST, DataTag.INTEGRATION]
            for tag in testing_tags:
                result = await _clean_by_data_tag(connection, tag)
                if result["nodes"] or result["relationships"]:
                    cleaned_results["data_tags"].append(tag.value)
                    for node_type, count in result["nodes"].items():
                        cleaned_results["cleaned_nodes"][node_type] = \
                            cleaned_results["cleaned_nodes"].get(node_type, 0) + count
                    cleaned_results["cleaned_relationships"] += result["relationships"]
        
        logger.info("测试数据清理完成", cleaned_results=cleaned_results)
        return cleaned_results
        
    except Exception as e:
        logger.error("清理测试数据失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")


async def _clean_by_test_marker(connection: Neo4jConnection, marker: str) -> Dict[str, Any]:
    """基于测试标识符清理数据"""
    
    # 查询包含测试标识符的节点
    query_nodes = """
    MATCH (n)
    WHERE n.comment CONTAINS $marker 
       OR n.alert_detail_id CONTAINS $marker
       OR n.reason CONTAINS $marker
       OR n.evidence_id CONTAINS $marker
       OR n.name CONTAINS $marker
    RETURN labels(n) as node_labels, count(n) as node_count
    """
    
    # 查询关联的关系
    query_relationships = """
    MATCH (n)-[r]-(m)
    WHERE n.comment CONTAINS $marker 
       OR n.alert_detail_id CONTAINS $marker
       OR n.reason CONTAINS $marker
       OR n.evidence_id CONTAINS $marker
       OR n.name CONTAINS $marker
       OR m.comment CONTAINS $marker 
       OR m.alert_detail_id CONTAINS $marker
       OR m.reason CONTAINS $marker
       OR m.evidence_id CONTAINS $marker
       OR m.name CONTAINS $marker
    RETURN count(r) as relationship_count
    """
    
    # 删除数据
    delete_query = """
    MATCH (n)
    WHERE n.comment CONTAINS $marker 
       OR n.alert_detail_id CONTAINS $marker
       OR n.reason CONTAINS $marker
       OR n.evidence_id CONTAINS $marker
       OR n.name CONTAINS $marker
    OPTIONAL MATCH (n)-[r]-()
    DELETE r, n
    RETURN count(DISTINCT n) as deleted_nodes, count(DISTINCT r) as deleted_relationships
    """
    
    try:
        # 统计节点
        node_results = connection.execute_read_transaction(query_nodes, {"marker": marker})
        nodes_by_type = {}
        for record in node_results:
            labels = record["node_labels"]
            count = record["node_count"]
            if labels:
                label = labels[0]  # 取第一个标签
                nodes_by_type[label] = nodes_by_type.get(label, 0) + count
        
        # 统计关系
        rel_results = connection.execute_read_transaction(query_relationships, {"marker": marker})
        total_relationships = rel_results[0]["relationship_count"] if rel_results else 0
        
        # 执行删除
        delete_results = connection.execute_write_transaction(delete_query, {"marker": marker})
        
        return {
            "nodes": nodes_by_type,
            "relationships": total_relationships,
            "deleted_nodes": delete_results[0]["deleted_nodes"] if delete_results else 0,
            "deleted_relationships": delete_results[0]["deleted_relationships"] if delete_results else 0
        }
        
    except Exception as e:
        logger.error(f"基于标识符清理失败", marker=marker, error=str(e))
        return {"nodes": {}, "relationships": 0}


async def _clean_by_data_tag(connection: Neo4jConnection, tag: DataTag) -> Dict[str, Any]:
    """基于数据标签清理数据"""
    
    # 查询特定标签的节点
    query_nodes = """
    MATCH (n)
    WHERE n.data_tag = $tag_value
    RETURN labels(n) as node_labels, count(n) as node_count
    """
    
    # 查询特定标签的关系
    query_relationships = """
    MATCH ()-[r]->()
    WHERE r.data_tag = $tag_value
    RETURN count(r) as relationship_count
    """
    
    # 删除数据 (先删除关系，再删除节点)
    delete_query = """
    MATCH (n)
    WHERE n.data_tag = $tag_value
    OPTIONAL MATCH (n)-[r]-()
    WHERE r.data_tag = $tag_value OR r.data_tag IS NULL
    DELETE r, n
    RETURN count(DISTINCT n) as deleted_nodes, count(DISTINCT r) as deleted_relationships
    """
    
    try:
        # 统计节点
        node_results = connection.execute_read_transaction(query_nodes, {"tag_value": tag.value})
        nodes_by_type = {}
        for record in node_results:
            labels = record["node_labels"]
            count = record["node_count"]
            if labels:
                label = labels[0]  # 取第一个标签
                nodes_by_type[label] = nodes_by_type.get(label, 0) + count
        
        # 统计关系
        rel_results = connection.execute_read_transaction(query_relationships, {"tag_value": tag.value})
        total_relationships = rel_results[0]["relationship_count"] if rel_results else 0
        
        # 执行删除
        delete_results = connection.execute_write_transaction(delete_query, {"tag_value": tag.value})
        
        return {
            "nodes": nodes_by_type,
            "relationships": total_relationships,
            "deleted_nodes": delete_results[0]["deleted_nodes"] if delete_results else 0,
            "deleted_relationships": delete_results[0]["deleted_relationships"] if delete_results else 0
        }
        
    except Exception as e:
        logger.error(f"基于标签清理失败", tag=tag.value, error=str(e))
        return {"nodes": {}, "relationships": 0}


@router.get("/stats")
async def get_system_stats(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """获取系统统计信息"""
    try:
        # 统计各类型节点数量
        query = """
        MATCH (n)
        RETURN labels(n) as node_labels, count(n) as node_count
        """
        
        results = connection.execute_read_transaction(query)
        stats = {
            "total_nodes": 0,
            "nodes_by_type": {},
            "total_relationships": 0
        }
        
        for record in results:
            labels = record["node_labels"]
            count = record["node_count"]
            stats["total_nodes"] += count
            if labels:
                label = labels[0]
                stats["nodes_by_type"][label] = stats["nodes_by_type"].get(label, 0) + count
        
        # 统计关系数量
        rel_query = "MATCH ()-[r]->() RETURN count(r) as relationship_count"
        rel_results = connection.execute_read_transaction(rel_query)
        if rel_results:
            stats["total_relationships"] = rel_results[0]["relationship_count"]
        
        return stats
        
    except Exception as e:
        logger.error("获取系统统计失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


@router.get("/tags")
async def get_data_tags(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """获取数据库中的所有数据标签统计"""
    try:
        query = """
        MATCH (n)
        WHERE n.data_tag IS NOT NULL
        RETURN n.data_tag as tag, labels(n) as node_labels, count(n) as count
        ORDER BY tag, node_labels
        """
        
        results = connection.execute_read_transaction(query)
        tag_stats = {}
        
        for record in results:
            tag = record["tag"]
            labels = record["node_labels"]
            count = record["count"]
            
            if tag not in tag_stats:
                tag_stats[tag] = {"total": 0, "by_type": {}}
            
            tag_stats[tag]["total"] += count
            if labels:
                label = labels[0]
                tag_stats[tag]["by_type"][label] = tag_stats[tag]["by_type"].get(label, 0) + count
        
        return tag_stats
        
    except Exception as e:
        logger.error("获取标签统计失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取标签统计失败: {str(e)}") 