"""
告警管理服务

实现告警细节的创建、查询、更新等业务逻辑
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import uuid4

from app.database.connection import Neo4jConnection
from app.repositories.base import BaseRepository
from app.models.alert import (
    AlertDetailCreate,
    AlertDetailUpdate,
    AlertDetailResponse,
    AlertSearchFilters,
    BatchCreateResponse,
)
from app.models.base import AlertStatus
from app.services.evidence_service import EvidenceService
from app.services.device_service import DeviceService
from app.utils.logger import logger, log_execution_time, log_database_operation
from app.utils.tag_utils import TagManager


class AlertService:
    """告警管理服务"""

    def __init__(self, connection: Neo4jConnection):
        self.connection = connection
        self.repository = BaseRepository(connection)
        self.evidence_service = EvidenceService(connection)
        self.device_service = DeviceService(connection)

    @log_execution_time("创建告警细节", min_duration=2.0)
    def create_alert_detail(self, alert_data: AlertDetailCreate) -> AlertDetailResponse:
        """创建单个告警细节"""
        try:
            # 生成唯一ID
            vid = str(uuid4())
            current_time = datetime.now()

            # 解析数据标签
            data_tag = TagManager.resolve_tag(
                explicit_tag=alert_data.data_tag,
                default_tag=TagManager.get_current_tag(),
            )

            # 简化日志 - 只记录开始和关键信息
            logger.business_operation(
                "create_alert_detail",
                entity_id=alert_data.alert_detail_id,
                vid=vid,
                data_tag=data_tag.value,
                has_evidence=bool(alert_data.evidence_artifacts),
            )

            # 准备节点属性
            properties = {
                "vid": vid,
                "alert_detail_id": alert_data.alert_detail_id,
                "alert_name": alert_data.alert_name,
                "alert_message": alert_data.alert_message,
                "vulnerabilities": alert_data.vulnerabilities,
                "product_name": alert_data.product_name,
                "time": current_time.isoformat(),
                "confidence_score": alert_data.confidence_score,
                "impact": alert_data.impact.value,
                "risk": alert_data.risk.value,
                "severity": alert_data.severity.value,
                "status": alert_data.status.value,
                "comment": alert_data.comment,
                "raw_data": alert_data.raw_data,
                "created_at": current_time.isoformat(),
                "updated_at": current_time.isoformat(),
            }

            # 添加数据标签
            properties = TagManager.add_tag_to_properties(properties, data_tag)

            # 创建告警细节节点
            node_id = self.repository.create_node("AlertDetail", properties)

            # 处理evidence_artifacts（如果存在）
            if alert_data.evidence_artifacts:
                evidence_id = self.evidence_service.process_evidence_artifacts(
                    vid, alert_data.evidence_artifacts, data_tag
                )

            # 提取并创建设备信息
            device_id = self.device_service.extract_and_create_device(
                alert_data, data_tag
            )
            if device_id:
                self.device_service.link_alert_to_device(vid, device_id, data_tag)

            # 返回响应模型
            return AlertDetailResponse(
                vid=vid,
                alert_detail_id=alert_data.alert_detail_id,
                alert_name=alert_data.alert_name,
                alert_message=alert_data.alert_message,
                vulnerabilities=alert_data.vulnerabilities,
                product_name=alert_data.product_name,
                time=current_time,
                confidence_score=alert_data.confidence_score,
                impact=alert_data.impact,
                risk=alert_data.risk,
                severity=alert_data.severity,
                status=alert_data.status,
                comment=alert_data.comment,
                raw_data=alert_data.raw_data,
                data_tag=data_tag,
                created_at=current_time,
                updated_at=current_time,
            )
        except Exception as e:
            logger.error(
                f"创建告警细节失败", error=e, alert_detail_id=alert_data.alert_detail_id
            )
            raise

    @log_execution_time("批量创建告警", min_duration=5.0)
    def batch_create_alerts(
        self, alerts: List[AlertDetailCreate]
    ) -> BatchCreateResponse:
        """批量创建告警细节"""
        try:
            success_count = 0
            failed_count = 0
            created_ids = []
            errors = []

            # 只记录开始信息
            logger.business_operation("batch_create_alerts", alert_count=len(alerts))

            for i, alert_data in enumerate(alerts):
                try:
                    result = self.create_alert_detail(alert_data)
                    success_count += 1
                    created_ids.append(result.vid)
                except Exception as e:
                    failed_count += 1
                    errors.append(
                        {
                            "index": i,
                            "alert_detail_id": alert_data.alert_detail_id,
                            "error": str(e),
                        }
                    )
                    # 只记录失败的告警
                    logger.error(
                        f"告警创建失败",
                        alert_detail_id=alert_data.alert_detail_id,
                        error=e,
                    )

            # 记录最终结果
            if failed_count > 0:
                logger.warning(
                    f"批量创建完成，有失败项",
                    success_count=success_count,
                    failed_count=failed_count,
                    total_count=len(alerts),
                )

            return BatchCreateResponse(
                success_count=success_count,
                failed_count=failed_count,
                created_ids=created_ids,
                errors=errors,
            )

        except Exception as e:
            logger.error(f"批量创建告警失败", error=e, alert_count=len(alerts))
            raise

    def get_alert_by_id(self, alert_id: str) -> Optional[AlertDetailResponse]:
        """根据ID获取告警细节（支持vid或alert_detail_id）"""
        try:
            query = """
            MATCH (a:AlertDetail)
            WHERE a.vid = $alert_id OR a.alert_detail_id = $alert_id
            RETURN a
            """

            result = self.connection.execute_read_transaction(
                query, {"alert_id": alert_id}
            )

            if not result:
                return None

            node_data = result[0]["a"]
            return self._convert_to_response(node_data)

        except Exception as e:
            raise Exception(f"获取告警详情失败: {str(e)}")

    def get_alerts_by_device(self, device_id: str) -> List[AlertDetailResponse]:
        """根据设备ID获取告警列表"""
        try:
            query = """
            MATCH (d:Device {device_id: $device_id})<-[:GENERATED_BY]-(a:AlertDetail)
            RETURN a
            ORDER BY a.time DESC
            """

            result = self.connection.execute_read_transaction(
                query, {"device_id": device_id}
            )

            return [self._convert_to_response(record["a"]) for record in result]

        except Exception as e:
            raise Exception(f"获取设备告警列表失败: {str(e)}")

    def get_alerts_by_time_range(
        self, start_time: datetime, end_time: datetime
    ) -> List[AlertDetailResponse]:
        """根据时间范围获取告警列表"""
        try:
            query = """
            MATCH (a:AlertDetail)
            WHERE datetime(a.time) >= datetime($start_time) 
              AND datetime(a.time) <= datetime($end_time)
            RETURN a
            ORDER BY a.time DESC
            """

            result = self.connection.execute_read_transaction(
                query,
                {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                },
            )

            return [self._convert_to_response(record["a"]) for record in result]

        except Exception as e:
            raise Exception(f"按时间范围查询告警失败: {str(e)}")

    def update_alert_status(self, alert_id: str, status: AlertStatus) -> bool:
        """更新告警状态"""
        try:
            query = """
            MATCH (a:AlertDetail)
            WHERE a.vid = $alert_id OR a.alert_detail_id = $alert_id
            SET a.status = $status, a.updated_at = $updated_at
            RETURN a
            """

            result = self.connection.execute_write_transaction(
                query,
                {
                    "alert_id": alert_id,
                    "status": status.value,
                    "updated_at": datetime.now().isoformat(),
                },
            )

            return len(result) > 0

        except Exception as e:
            raise Exception(f"更新告警状态失败: {str(e)}")

    def update_alert_fields(
        self, alert_id: str, update_data: AlertDetailUpdate
    ) -> Optional[AlertDetailResponse]:
        """更新告警字段"""
        try:
            # 构建动态更新语句
            set_clauses = []
            params = {"alert_id": alert_id, "updated_at": datetime.now().isoformat()}

            if update_data.alert_name is not None:
                set_clauses.append("a.alert_name = $alert_name")
                params["alert_name"] = update_data.alert_name

            if update_data.alert_message is not None:
                set_clauses.append("a.alert_message = $alert_message")
                params["alert_message"] = update_data.alert_message

            if update_data.status is not None:
                set_clauses.append("a.status = $status")
                params["status"] = update_data.status.value

            if update_data.impact is not None:
                set_clauses.append("a.impact = $impact")
                params["impact"] = update_data.impact.value

            if update_data.risk is not None:
                set_clauses.append("a.risk = $risk")
                params["risk"] = update_data.risk.value

            if update_data.severity is not None:
                set_clauses.append("a.severity = $severity")
                params["severity"] = update_data.severity.value

            if update_data.comment is not None:
                set_clauses.append("a.comment = $comment")
                params["comment"] = update_data.comment

            if not set_clauses:
                return self.get_alert_by_id(alert_id)

            query = f"""
            MATCH (a:AlertDetail)
            WHERE a.vid = $alert_id OR a.alert_detail_id = $alert_id
            SET {", ".join(set_clauses)}, a.updated_at = $updated_at
            RETURN a
            """

            result = self.connection.execute_write_transaction(query, params)

            if not result:
                return None

            node_data = result[0]["a"]
            return self._convert_to_response(node_data)

        except Exception as e:
            raise Exception(f"更新告警字段失败: {str(e)}")

    def get_alert_related_entities(self, alert_id: str) -> List[Dict[str, Any]]:
        """通过证据节点获取告警相关的风险实体"""
        try:
            query = """
            MATCH (a:AlertDetail {vid: $alert_id})-[:HAS_EVIDENCE]->(ev:Evidence)-[:RELATES_TO]->(entity)
            RETURN entity, labels(entity) as entity_labels,
                   collect(distinct ev.evidence_id) as evidence_ids
            """

            result = self.connection.execute_read_transaction(
                query, {"alert_id": alert_id}
            )

            entities = []
            for record in result:
                entity_data = dict(record["entity"])
                entity_labels = record["entity_labels"]

                # 确定实体类型（取第一个非基础标签）
                entity_type = "Unknown"
                for label in entity_labels:
                    if label not in ["Entity"]:  # 排除基础标签
                        entity_type = label
                        break

                entity_data["entity_type"] = entity_type
                entity_data["entity_labels"] = entity_labels
                entity_data["evidence_ids"] = record["evidence_ids"]

                # 添加实体ID字段（如果存在）
                if "entity_id" not in entity_data and "vid" in entity_data:
                    entity_data["entity_id"] = entity_data["vid"]

                entities.append(entity_data)

            return entities

        except Exception as e:
            raise Exception(f"获取告警相关实体失败: {str(e)}")

    def _convert_to_response(self, node_data: dict) -> AlertDetailResponse:
        """将Neo4j节点数据转换为响应模型"""
        return AlertDetailResponse(
            vid=node_data.get("vid"),
            alert_detail_id=node_data.get("alert_detail_id"),
            alert_name=node_data.get("alert_name"),
            alert_message=node_data.get("alert_message"),
            vulnerabilities=node_data.get("vulnerabilities"),
            product_name=node_data.get("product_name"),
            time=datetime.fromisoformat(node_data.get("time")),
            confidence_score=node_data.get("confidence_score"),
            impact=node_data.get("impact"),
            risk=node_data.get("risk"),
            severity=node_data.get("severity"),
            status=node_data.get("status"),
            comment=node_data.get("comment"),
            raw_data=node_data.get("raw_data"),
            created_at=datetime.fromisoformat(node_data.get("created_at")),
            updated_at=datetime.fromisoformat(node_data.get("updated_at")),
        )
