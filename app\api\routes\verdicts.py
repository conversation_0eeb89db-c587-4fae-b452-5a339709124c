"""
研判管理API路由

提供研判相关的REST API接口
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException
from app.api.dependencies import get_verdict_service
from app.services.verdict_service import VerdictService
from app.models.alert import (
    VerdictCreate,
    VerdictUpdate,
    VerdictResponse,
    BatchVerdictResponse,
    BatchCreateResponse
)

router = APIRouter(prefix="/verdicts", tags=["研判管理"])


@router.post("/", response_model=VerdictResponse)
def create_verdict(
    verdict_data: VerdictCreate,
    verdict_service: VerdictService = Depends(get_verdict_service)
):
    """创建研判记录"""
    try:
        result = verdict_service.create_verdict(verdict_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建研判记录失败: {str(e)}")


@router.get("/{verdict_id}", response_model=VerdictResponse)
def get_verdict(
    verdict_id: str,
    verdict_service: VerdictService = Depends(get_verdict_service)
):
    """获取研判记录"""
    try:
        verdict = verdict_service.get_verdict_by_id(verdict_id)
        if not verdict:
            raise HTTPException(status_code=404, detail="研判记录不存在")
        return verdict
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取研判记录失败: {str(e)}")


@router.put("/{verdict_id}", response_model=VerdictResponse)
def update_verdict(
    verdict_id: str,
    update_data: VerdictUpdate,
    verdict_service: VerdictService = Depends(get_verdict_service)
):
    """更新研判记录"""
    try:
        verdict = verdict_service.update_verdict(verdict_id, update_data)
        if not verdict:
            raise HTTPException(status_code=404, detail="研判记录不存在")
        return verdict
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新研判记录失败: {str(e)}")


@router.post("/batch", response_model=BatchCreateResponse)
def batch_apply_verdict(
    batch_data: dict,
    verdict_service: VerdictService = Depends(get_verdict_service)
):
    """批量应用研判"""
    try:
        alert_ids = batch_data.get("alert_ids", [])
        verdict_data = VerdictCreate(**batch_data.get("verdict_data", {}))
        result = verdict_service.batch_apply_verdict(alert_ids, verdict_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量应用研判失败: {str(e)}")


# 告警研判相关接口
alerts_verdicts_router = APIRouter(prefix="/alerts", tags=["告警研判"])


@alerts_verdicts_router.post("/{alert_id}/verdicts", response_model=VerdictResponse)
def add_verdict_to_alert(
    alert_id: str,
    verdict_data: VerdictCreate,
    verdict_service: VerdictService = Depends(get_verdict_service)
):
    """为告警添加研判记录"""
    try:
        result = verdict_service.add_verdict_to_alert(alert_id, verdict_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"为告警添加研判失败: {str(e)}")


@alerts_verdicts_router.get("/{alert_id}/verdicts", response_model=List[VerdictResponse])
def get_alert_verdicts(
    alert_id: str,
    verdict_service: VerdictService = Depends(get_verdict_service)
):
    """获取告警的研判记录列表"""
    try:
        result = verdict_service.get_verdicts_by_alert(alert_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取告警研判记录失败: {str(e)}")


@alerts_verdicts_router.get("/{alert_id}/verdicts/latest", response_model=VerdictResponse)
def get_latest_verdict(
    alert_id: str,
    verdict_service: VerdictService = Depends(get_verdict_service)
):
    """获取告警的最新研判记录"""
    try:
        verdict = verdict_service.get_latest_verdict(alert_id)
        if not verdict:
            raise HTTPException(status_code=404, detail="该告警暂无研判记录")
        return verdict
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取最新研判记录失败: {str(e)}")


@alerts_verdicts_router.post("/batch/verdict", response_model=BatchVerdictResponse)
def batch_apply_verdict(
    alert_ids: List[str],
    verdict_data: VerdictCreate,
    verdict_service: VerdictService = Depends(get_verdict_service)
):
    """批量应用研判"""
    try:
        result = verdict_service.batch_apply_verdict(alert_ids, verdict_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量应用研判失败: {str(e)}")


@alerts_verdicts_router.post("/{reference_alert_id}/verdicts/similar", response_model=BatchVerdictResponse)
def apply_verdict_to_similar(
    reference_alert_id: str,
    verdict_data: VerdictCreate,
    verdict_service: VerdictService = Depends(get_verdict_service)
):
    """对相似告警应用研判"""
    try:
        result = verdict_service.apply_verdict_to_similar_alerts(reference_alert_id, verdict_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"对相似告警应用研判失败: {str(e)}") 