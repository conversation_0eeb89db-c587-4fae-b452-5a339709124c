"""
数据模型包

包含所有Pydantic数据模型定义
"""

# 基础模型
from .base import (
    TimestampedModel,
    PaginationModel,
    ResponseModel,
    ErrorResponse,
    PaginatedResponse,
    AlertStatus,
    Severity,
    Impact,
    Risk,
    VerdictType,
    VerdictLabel,
    EntityType,
    DataTag,
)

# 告警相关模型
from .alert import (
    AlertDetailBase,
    AlertDetailCreate,
    AlertDetailUpdate,
    AlertDetailResponse,
    EvidenceBase,
    EvidenceCreate,
    EvidenceResponse,
    DeviceBase,
    DeviceCreate,
    DeviceResponse,
    AlertSearchFilters,
    BatchCreateResponse,
    EvidenceArtifactsModel,
    HttpRequestModel,
    HttpResponseModel,
    DnsQueryModel,
    NetworkEndpointModel,
    UrlModel,
    FileModel,
    ProcessModel,
    UserModel,
)

# 研判相关模型（独立模块）
from .verdict import (
    VerdictCreate,
    VerdictUpdate,
    VerdictResponse,
    VerdictQuery,
    VerdictListResponse,
    VerdictStatistics,
)

# 实体相关模型
from .entity import (
    EntityBase,
    EntityCreate,
    EntityUpdate,
    EntityResponse,
    RelationshipBase,
    RelationshipCreate,
    RelationshipResponse,
    EntitySearchParams,
    PathResponse,
    HttpRequestEntity,
    HttpResponseEntity,
    DnsQueryEntity,
    NetworkEndpointEntity,
    UrlEntity,
    FileEntity,
    ProcessEntity,
)

__all__ = [
    # 基础模型
    "TimestampedModel",
    "PaginationModel",
    "ResponseModel",
    "ErrorResponse",
    "PaginatedResponse",
    "AlertStatus",
    "Severity",
    "Impact",
    "Risk",
    "VerdictType",
    "VerdictLabel",
    "EntityType",
    "DataTag",
    # 告警相关模型
    "AlertDetailBase",
    "AlertDetailCreate",
    "AlertDetailUpdate",
    "AlertDetailResponse",
    "VerdictCreate",
    "VerdictUpdate",
    "VerdictResponse",
    "VerdictQuery",
    "VerdictListResponse",
    "VerdictStatistics",
    "EvidenceBase",
    "EvidenceCreate",
    "EvidenceResponse",
    "DeviceBase",
    "DeviceCreate",
    "DeviceResponse",
    "AlertSearchFilters",
    "BatchCreateResponse",
    "EvidenceArtifactsModel",
    "HttpRequestModel",
    "HttpResponseModel",
    "DnsQueryModel",
    "NetworkEndpointModel",
    "UrlModel",
    "FileModel",
    "ProcessModel",
    "UserModel",
    # 实体相关模型
    "EntityBase",
    "EntityCreate",
    "EntityUpdate",
    "EntityResponse",
    "RelationshipBase",
    "RelationshipCreate",
    "RelationshipResponse",
    "EntitySearchParams",
    "PathResponse",
    "HttpRequestEntity",
    "HttpResponseEntity",
    "DnsQueryEntity",
    "NetworkEndpointEntity",
    "UrlEntity",
    "FileEntity",
    "ProcessEntity",
]
