"""
基础数据模型

定义通用的Pydantic模型和基础类
"""

from datetime import datetime
from typing import Optional, Any, Dict
from pydantic import BaseModel, Field, ConfigDict
from enum import Enum


class TimestampedModel(BaseModel):
    """带时间戳的基础模型"""
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    data_tag: Optional[str] = Field("production", description="数据标签 - 用于区分数据环境")


class PaginationModel(BaseModel):
    """分页模型"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页数量")
    total: Optional[int] = Field(None, description="总数量")


class ResponseModel(BaseModel):
    """通用响应模型"""
    success: bool = Field(True, description="是否成功")
    message: str = Field("", description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


class PaginatedResponse(ResponseModel):
    """分页响应模型"""
    data: Optional[Any] = Field(None, description="数据列表")
    pagination: Optional[PaginationModel] = Field(None, description="分页信息")


# 枚举类型定义
class AlertStatus(str, Enum):
    """告警状态枚举"""
    NEW = "New"
    IN_PROGRESS = "InProgress"
    SUPPRESSED = "Suppressed"
    RESOLVED = "Resolved"
    ARCHIVED = "Archived"
    UNKNOWN = "Unknown"


class Severity(str, Enum):
    """严重性等级枚举"""
    FATAL = "Fatal"
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    UNKNOWN = "Unknown"


class Impact(str, Enum):
    """影响等级枚举"""
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    UNKNOWN = "Unknown"


class Risk(str, Enum):
    """风险等级枚举"""
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    UNKNOWN = "Unknown"


class VerdictType(int, Enum):
    """研判类型枚举"""
    AI = 1
    MANUAL = 2


class VerdictLabel(int, Enum):
    """研判标签枚举"""
    FALSE_POSITIVE = 1  # 误报
    CONFIRMED_ATTACK = 2  # 确认攻击
    SUSPICIOUS = 3  # 可疑
    INSUFFICIENT_DATA = 4  # 数据不足


class EntityType(str, Enum):
    """实体类型枚举"""
    HTTP_REQUEST = "HttpRequestEntity"
    HTTP_RESPONSE = "HttpResponseEntity"
    DNS_QUERY = "DnsQueryEntity"
    NETWORK_ENDPOINT = "NetworkEndpointEntity"
    URL = "UrlEntity"
    FILE = "FileEntity"
    PROCESS = "ProcessEntity"
    DEVICE = "Device"


class DataTag(str, Enum):
    """数据标签枚举 - 用于区分数据环境"""
    PRODUCTION = "production"      # 正式环境数据
    TESTING = "testing"           # 测试环境数据
    DEVELOPMENT = "development"   # 开发环境数据
    DEMO = "demo"                # 演示数据
    BENCHMARK = "benchmark"      # 性能测试数据
    INTEGRATION = "integration"  # 集成测试数据
    UNIT_TEST = "unit_test"      # 单元测试数据 