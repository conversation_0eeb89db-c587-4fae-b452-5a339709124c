"""
项目常量定义

包含系统中使用的各种常量、枚举值等
"""

from enum import Enum


# 节点标签定义
class NodeLabels:
    """图数据库节点标签"""
    ALERT_DETAIL = "AlertDetail"
    VERDICT = "Verdict"
    EVIDENCE = "Evidence"
    DEVICE = "Device"
    HTTP_REQUEST_ENTITY = "HttpRequestEntity"
    HTTP_RESPONSE_ENTITY = "HttpResponseEntity"
    DNS_QUERY_ENTITY = "DnsQueryEntity"
    NETWORK_ENDPOINT_ENTITY = "NetworkEndpointEntity"
    URL_ENTITY = "UrlEntity"
    FILE_ENTITY = "FileEntity"
    PROCESS_ENTITY = "ProcessEntity"


# 关系类型定义
class RelationshipTypes:
    """图数据库关系类型"""
    HAS_VERDICT = "HAS_VERDICT"            # 告警细节->研判记录
    HAS_EVIDENCE = "HAS_EVIDENCE"          # 告警细节->证据信息
    RELATES_TO = "RELATES_TO"              # 证据信息->风险实体
    GENERATED_BY = "GENERATED_BY"          # 告警细节->设备信息
    BELONGS_TO = "BELONGS_TO"              # 告警细节->告警会话
    CONTAINS = "CONTAINS"                  # 告警会话->告警事件


# 告警相关枚举
class AlertImpact(str, Enum):
    """告警影响等级"""
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    UNKNOWN = "Unknown"


class AlertRisk(str, Enum):
    """告警风险等级"""
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    UNKNOWN = "Unknown"


class AlertSeverity(str, Enum):
    """告警严重性"""
    FATAL = "Fatal"
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    UNKNOWN = "Unknown"


class AlertStatus(str, Enum):
    """告警处理状态"""
    NEW = "New"
    IN_PROGRESS = "InProgress"
    SUPPRESSED = "Suppressed"
    RESOLVED = "Resolved"
    ARCHIVED = "Archived"
    UNKNOWN = "Unknown"


# 研判相关枚举
class VerdictType(int, Enum):
    """研判类型"""
    AI = 1      # AI研判
    MANUAL = 2  # 人工研判


class VerdictLabel(int, Enum):
    """研判标签"""
    FALSE_POSITIVE = 1    # 误报
    CONFIRMED_ATTACK = 2  # 确认攻击
    SUSPICIOUS = 3        # 可疑
    INSUFFICIENT_DATA = 4 # 数据不足


# API响应状态码
class ResponseStatus:
    """API响应状态"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"


# 分页默认值
DEFAULT_PAGE = 1
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100 