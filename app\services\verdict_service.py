"""
研判管理服务

实现研判记录的创建、查询、更新等业务逻辑
"""

from datetime import datetime
from typing import List, Optional
from uuid import uuid4

from app.database.connection import Neo4jConnection
from app.repositories.base import BaseRepository
from app.models.verdict import (
    VerdictCreate,
    VerdictUpdate,
    VerdictResponse,
)


class VerdictService:
    """研判管理服务"""

    def __init__(self, connection: Neo4jConnection):
        self.connection = connection
        self.repository = BaseRepository(connection)

    def create_verdict(self, verdict_data: VerdictCreate) -> VerdictResponse:
        """创建研判记录"""
        try:
            # 生成唯一ID
            verdict_id = str(uuid4())
            current_time = datetime.now()

            # 准备节点属性
            properties = {
                "verdict_id": verdict_id,
                "type_id": verdict_data.type_id.value,
                "type": verdict_data.type,
                "label": verdict_data.label.value,
                "reason": verdict_data.reason,
                "commiter": verdict_data.commiter,
                "time": current_time.isoformat(),
                "comment": verdict_data.comment,
                "created_at": current_time.isoformat(),
                "updated_at": current_time.isoformat(),
            }

            # 添加数据标签
            from app.utils.tag_utils import TagManager

            properties = TagManager.add_tag_to_properties(
                properties, verdict_data.data_tag
            )

            # 创建节点
            self.repository.create_node("Verdict", properties)

            # 返回响应模型
            return VerdictResponse(
                vid=verdict_id,  # 添加vid字段
                verdict_id=verdict_id,
                type_id=verdict_data.type_id,
                type=verdict_data.type,
                label=verdict_data.label,
                label_name=self._get_label_name(verdict_data.label),
                reason=verdict_data.reason,
                commiter=verdict_data.commiter,
                time=current_time,
                comment=verdict_data.comment,
                created_at=current_time,
                updated_at=current_time,
                data_tag=verdict_data.data_tag.value if verdict_data.data_tag else None,
            )

        except Exception as e:
            raise Exception(f"创建研判记录失败: {str(e)}")

    def add_verdict_to_alert(
        self, alert_id: str, verdict_data: VerdictCreate
    ) -> VerdictResponse:
        """为告警添加研判记录"""
        try:
            # 创建研判记录
            verdict = self.create_verdict(verdict_data)

            # 建立告警与研判的关系
            query = """
            MATCH (a:AlertDetail)
            WHERE a.vid = $alert_id OR a.alert_detail_id = $alert_id
            MATCH (v:Verdict {verdict_id: $verdict_id})
            CREATE (a)-[:HAS_VERDICT]->(v)
            RETURN a, v
            """

            result = self.connection.execute_write_transaction(
                query, {"alert_id": alert_id, "verdict_id": verdict.verdict_id}
            )

            if not result:
                raise Exception("告警不存在或关联失败")

            return verdict

        except Exception as e:
            raise Exception(f"为告警添加研判记录失败: {str(e)}")

    def get_verdicts_by_alert(self, alert_id: str) -> List[VerdictResponse]:
        """获取告警的研判记录列表"""
        try:
            query = """
            MATCH (a:AlertDetail)-[:HAS_VERDICT]->(v:Verdict)
            WHERE a.vid = $alert_id OR a.alert_detail_id = $alert_id
            RETURN v
            ORDER BY v.time DESC
            """

            result = self.connection.execute_read_transaction(
                query, {"alert_id": alert_id}
            )

            return [self._convert_to_response(record["v"]) for record in result]

        except Exception as e:
            raise Exception(f"获取告警研判记录失败: {str(e)}")

    def get_latest_verdict(self, alert_id: str) -> Optional[VerdictResponse]:
        """获取告警的最新研判记录"""
        try:
            query = """
            MATCH (a:AlertDetail)-[:HAS_VERDICT]->(v:Verdict)
            WHERE a.vid = $alert_id OR a.alert_detail_id = $alert_id
            RETURN v
            ORDER BY v.time DESC
            LIMIT 1
            """

            result = self.connection.execute_read_transaction(
                query, {"alert_id": alert_id}
            )

            if not result:
                return None

            return self._convert_to_response(result[0]["v"])

        except Exception as e:
            raise Exception(f"获取最新研判记录失败: {str(e)}")

    def update_verdict(
        self, verdict_id: str, update_data: VerdictUpdate
    ) -> Optional[VerdictResponse]:
        """更新研判记录"""
        try:
            # 构建动态更新语句
            set_clauses = []
            params = {
                "verdict_id": verdict_id,
                "updated_at": datetime.now().isoformat(),
            }

            if update_data.type_id is not None:
                set_clauses.append("v.type_id = $type_id")
                params["type_id"] = update_data.type_id.value

            if update_data.type is not None:
                set_clauses.append("v.type = $type")
                params["type"] = update_data.type

            if update_data.label is not None:
                set_clauses.append("v.label = $label")
                params["label"] = update_data.label.value

            if update_data.reason is not None:
                set_clauses.append("v.reason = $reason")
                params["reason"] = update_data.reason

            if update_data.commiter is not None:
                set_clauses.append("v.commiter = $commiter")
                params["commiter"] = update_data.commiter

            if update_data.comment is not None:
                set_clauses.append("v.comment = $comment")
                params["comment"] = update_data.comment

            if not set_clauses:
                return self.get_verdict_by_id(verdict_id)

            query = f"""
            MATCH (v:Verdict {{verdict_id: $verdict_id}})
            SET {", ".join(set_clauses)}, v.updated_at = $updated_at
            RETURN v
            """

            result = self.connection.execute_write_transaction(query, params)

            if not result:
                return None

            return self._convert_to_response(result[0]["v"])

        except Exception as e:
            raise Exception(f"更新研判记录失败: {str(e)}")

    def get_verdict_by_id(self, verdict_id: str) -> Optional[VerdictResponse]:
        """根据ID获取研判记录"""
        try:
            query = """
            MATCH (v:Verdict {verdict_id: $verdict_id})
            RETURN v
            """

            result = self.connection.execute_read_transaction(
                query, {"verdict_id": verdict_id}
            )

            if not result:
                return None

            return self._convert_to_response(result[0]["v"])

        except Exception as e:
            raise Exception(f"获取研判记录失败: {str(e)}")

    def _convert_to_response(self, node_data: dict) -> VerdictResponse:
        """将Neo4j节点数据转换为响应模型"""
        return VerdictResponse(
            vid=node_data.get("verdict_id"),  # 使用verdict_id作为vid
            verdict_id=node_data.get("verdict_id"),
            type_id=node_data.get("type_id"),
            type=node_data.get("type"),
            label=node_data.get("label"),
            label_name=self._get_label_name(node_data.get("label")),
            reason=node_data.get("reason"),
            commiter=node_data.get("commiter"),
            time=datetime.fromisoformat(node_data.get("time")),
            comment=node_data.get("comment"),
            created_at=datetime.fromisoformat(node_data.get("created_at")),
            updated_at=datetime.fromisoformat(node_data.get("updated_at")),
            data_tag=node_data.get("data_tag"),
        )

    def _get_label_name(self, label: int) -> str:
        """获取研判标签名称"""
        label_mapping = {
            1: "误报",
            2: "确认攻击",
            3: "可疑",
            4: "数据不足",
        }
        return label_mapping.get(label, "未知标签")
