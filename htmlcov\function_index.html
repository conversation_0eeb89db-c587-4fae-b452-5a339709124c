<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">0%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-11 14:56 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app\__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html">app\api\__init__.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html#t12">app\api\dependencies.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html#t12"><data value='get_neo4j_connection'>get_neo4j_connection</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html#t17">app\api\dependencies.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html#t17"><data value='get_alert_service'>get_alert_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html#t24">app\api\dependencies.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html#t24"><data value='get_verdict_service'>get_verdict_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html#t31">app\api\dependencies.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html#t31"><data value='get_entity_service'>get_entity_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html">app\api\dependencies.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_dependencies_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31___init___py.html">app\api\routes\__init__.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t24">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t24"><data value='create_alert'>create_alert</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t36">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t36"><data value='batch_create_alerts'>batch_create_alerts</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t51">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t51"><data value='get_alert'>get_alert</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t68">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t68"><data value='update_alert'>update_alert</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t86">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t86"><data value='update_alert_status'>update_alert_status</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t104">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t104"><data value='get_alerts_by_time_range'>get_alerts_by_time_range</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t123">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t123"><data value='get_alerts_by_device'>get_alerts_by_device</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t136">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html#t136"><data value='get_alert_entities'>get_alert_entities</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html">app\api\routes\alerts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_alerts_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t28">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t28"><data value='create_entity'>create_entity</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t41">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t41"><data value='batch_create_entities'>batch_create_entities</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t54">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t54"><data value='get_entity'>get_entity</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t71">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t71"><data value='update_entity'>update_entity</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t89">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t89"><data value='search_entities'>search_entities</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t110">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t110"><data value='get_entities_by_type'>get_entities_by_type</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t123">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t123"><data value='create_relationship'>create_relationship</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t136">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t136"><data value='get_entity_relationships'>get_entity_relationships</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t149">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html#t149"><data value='find_entity_path'>find_entity_path</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html">app\api\routes\entities.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_entities_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t18">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t18"><data value='get_system_health'>get_system_health</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t37">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t37"><data value='get_system_stats'>get_system_stats</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t73">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t73"><data value='get_database_status'>get_database_status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t92">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t92"><data value='get_node_statistics'>get_node_statistics</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t128">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t128"><data value='get_relationship_statistics'>get_relationship_statistics</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t164">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t164"><data value='reset_test_database'>reset_test_database</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t188">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t188"><data value='clean_test_data'>clean_test_data</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t302">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t302"><data value='get_test_environment_status'>get_test_environment_status</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t325">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t325"><data value='get_recent_logs'>get_recent_logs</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t354">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html#t354"><data value='get_performance_metrics'>get_performance_metrics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html">app\api\routes\system.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_system_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t23">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t23"><data value='create_verdict'>create_verdict</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t36">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t36"><data value='get_verdict'>get_verdict</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t53">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t53"><data value='update_verdict'>update_verdict</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t71">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t71"><data value='batch_apply_verdict'>batch_apply_verdict</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t90">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t90"><data value='add_verdict_to_alert'>add_verdict_to_alert</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t104">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t104"><data value='get_alert_verdicts'>get_alert_verdicts</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t117">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t117"><data value='get_latest_verdict'>get_latest_verdict</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t134">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t134"><data value='batch_apply_verdict'>batch_apply_verdict</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t148">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html#t148"><data value='apply_verdict_to_similar'>apply_verdict_to_similar</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html">app\api\routes\verdicts.py</a></td>
                <td class="name left"><a href="z_2dd8bbcc83a1ab31_verdicts_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html">app\core\__init__.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t47">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t47"><data value='get_log_file_path'>Settings.get_log_file_path</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t53">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t53"><data value='get_error_log_file_path'>Settings.get_error_log_file_path</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t59">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t59"><data value='get_debug_log_file_path'>Settings.get_debug_log_file_path</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t66">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t66"><data value='get_effective_log_level'>Settings.get_effective_log_level</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html">app\core\constants.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_constants_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928___init___py.html">app\database\__init__.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t21">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t21"><data value='init__'>Neo4jConnection.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t37">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t37"><data value='connect'>Neo4jConnection.connect</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t52">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t52"><data value='get_driver'>Neo4jConnection.get_driver</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t58">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t58"><data value='close'>Neo4jConnection.close</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t65">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t65"><data value='is_connected'>Neo4jConnection.is_connected</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t76">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t76"><data value='execute_query'>Neo4jConnection.execute_query</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t96">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t96"><data value='execute_write_transaction'>Neo4jConnection.execute_write_transaction</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t107">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t107"><data value='write_tx'>Neo4jConnection.execute_write_transaction._write_tx</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t119">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t119"><data value='execute_read_transaction'>Neo4jConnection.execute_read_transaction</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t130">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t130"><data value='read_tx'>Neo4jConnection.execute_read_transaction._read_tx</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t142">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t142"><data value='execute_batch_write'>Neo4jConnection.execute_batch_write</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t152">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t152"><data value='batch_write_tx'>Neo4jConnection.execute_batch_write._batch_write_tx</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t170">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t170"><data value='get_session'>Neo4jConnection.get_session</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t179">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t179"><data value='health_check'>Neo4jConnection.health_check</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t209">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html#t209"><data value='ensure_connection_ready'>ensure_connection_ready</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html">app\database\connection.py</a></td>
                <td class="name left"><a href="z_de0c988eb77bc928_connection_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t20">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t20"><data value='lifespan'>lifespan</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t43">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t43"><data value='create_app'>create_app</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t73">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t73"><data value='root'>create_app.root</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t84">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t84"><data value='health_check'>create_app.health_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t89">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t89"><data value='database_health_check'>create_app.database_health_check</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t105">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t105"><data value='log_requests'>create_app.log_requests</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t132">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t132"><data value='configure_logging'>configure_logging</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t149">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t149"><data value='start_server'>start_server</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html">app\models\__init__.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t347">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html#t347"><data value='validate_time_range'>AlertSearchFilters.validate_time_range</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html">app\models\alert.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_alert_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>230</td>
                <td>230</td>
                <td>0</td>
                <td class="right" data-ratio="0 230">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html">app\models\base.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html">app\models\device.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_device_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html">app\models\entity.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_entity_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t149">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t149"><data value='get_verdict_type_name'>get_verdict_type_name</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t158">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html#t158"><data value='get_verdict_label_name'>get_verdict_label_name</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html">app\models\verdict.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_verdict_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf___init___py.html">app\repositories\__init__.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t22">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t22"><data value='init__'>BaseRepository.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t31">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t31"><data value='create_node'>BaseRepository.create_node</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t72">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t72"><data value='get_node_by_id'>BaseRepository.get_node_by_id</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t102">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t102"><data value='update_node'>BaseRepository.update_node</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t147">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t147"><data value='delete_node'>BaseRepository.delete_node</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t182">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t182"><data value='create_relationship'>BaseRepository.create_relationship</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t209">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t209"><data value='get_id_field'>BaseRepository.create_relationship.get_id_field</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t294">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t294"><data value='batch_create_nodes'>BaseRepository.batch_create_nodes</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t335">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t335"><data value='node_exists'>BaseRepository.node_exists</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t361">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t361"><data value='get_nodes_by_label'>BaseRepository.get_nodes_by_label</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t397">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html#t397"><data value='get_relationships'>BaseRepository.get_relationships</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html">app\repositories\base.py</a></td>
                <td class="name left"><a href="z_6f50d20dc3c7ccdf_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t30">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t30"><data value='init__'>AlertService.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t37">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t37"><data value='create_alert_detail'>AlertService.create_alert_detail</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t119">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t119"><data value='batch_create_alerts'>AlertService.batch_create_alerts</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t164">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t164"><data value='get_alert_by_id'>AlertService.get_alert_by_id</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t186">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t186"><data value='get_alerts_by_device'>AlertService.get_alerts_by_device</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t204">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t204"><data value='get_alerts_by_time_range'>AlertService.get_alerts_by_time_range</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t231">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t231"><data value='update_alert_status'>AlertService.update_alert_status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t254">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t254"><data value='update_alert_fields'>AlertService.update_alert_fields</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t314">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t314"><data value='get_alert_related_entities'>AlertService.get_alert_related_entities</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t339">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html#t339"><data value='convert_to_response'>AlertService._convert_to_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html">app\services\alert_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_alert_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t23">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t23"><data value='init__'>DeviceService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t27">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t27"><data value='extract_and_create_device'>DeviceService.extract_and_create_device</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t49">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t49"><data value='link_alert_to_device'>DeviceService.link_alert_to_device</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t76">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t76"><data value='extract_device_info'>DeviceService._extract_device_info</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t107">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t107"><data value='extract_from_evidence'>DeviceService._extract_from_evidence</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t146">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t146"><data value='extract_from_raw_data'>DeviceService._extract_from_raw_data</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t172">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t172"><data value='generate_device_id'>DeviceService._generate_device_id</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t190">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t190"><data value='get_device_by_id'>DeviceService._get_device_by_id</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t203">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t203"><data value='create_device_node'>DeviceService._create_device_node</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t231">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t231"><data value='increment_alert_count'>DeviceService._increment_alert_count</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t246">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t246"><data value='get_device_alerts'>DeviceService.get_device_alerts</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t257">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t257"><data value='get_device_statistics'>DeviceService.get_device_statistics</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t287">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html#t287"><data value='search_devices'>DeviceService.search_devices</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html">app\services\device_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_device_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t30">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t30"><data value='init__'>EntityService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t34">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t34"><data value='find_or_create_entity'>EntityService.find_or_create_entity</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t62">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t62"><data value='create_entity'>EntityService.create_entity</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t118">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t118"><data value='batch_create_entities'>EntityService.batch_create_entities</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t153">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t153"><data value='get_entity_by_id'>EntityService.get_entity_by_id</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t195">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t195"><data value='get_entities_by_type'>EntityService.get_entities_by_type</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t213">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t213"><data value='search_entities'>EntityService.search_entities</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t282">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t282"><data value='update_entity'>EntityService.update_entity</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t351">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t351"><data value='create_relationship'>EntityService.create_relationship</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t401">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t401"><data value='get_related_alerts'>EntityService.get_related_alerts</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t421">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t421"><data value='get_entity_relationships'>EntityService.get_entity_relationships</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t463">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t463"><data value='find_entity_path'>EntityService.find_entity_path</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t514">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t514"><data value='convert_to_response'>EntityService._convert_to_response</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t554">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t554"><data value='find_existing_entity'>EntityService._find_existing_entity</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t636">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t636"><data value='find_entity_with_body_hash'>EntityService._find_entity_with_body_hash</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t678">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t678"><data value='get_match_criteria'>EntityService._get_match_criteria</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t720">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t720"><data value='get_network_endpoint_criteria'>EntityService._get_network_endpoint_criteria</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t750">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t750"><data value='get_file_entity_criteria'>EntityService._get_file_entity_criteria</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t786">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t786"><data value='get_process_entity_criteria'>EntityService._get_process_entity_criteria</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t820">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t820"><data value='get_http_request_criteria'>EntityService._get_http_request_criteria</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t855">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t855"><data value='get_url_entity_criteria'>EntityService._get_url_entity_criteria</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t886">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t886"><data value='get_dns_query_criteria'>EntityService._get_dns_query_criteria</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t916">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t916"><data value='get_http_response_criteria'>EntityService._get_http_response_criteria</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t953">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t953"><data value='update_entity_on_reuse'>EntityService._update_entity_on_reuse</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t997">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html#t997"><data value='create_evidence_entity_relationship'>EntityService.create_evidence_entity_relationship</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html">app\services\entity_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_entity_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t26">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t26"><data value='init__'>EvidenceService.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t32">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t32"><data value='process_evidence_artifacts'>EvidenceService.process_evidence_artifacts</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t65">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t65"><data value='create_evidence_node'>EvidenceService._create_evidence_node</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t148">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t148"><data value='extract_risk_entities'>EvidenceService._extract_risk_entities</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t221">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t221"><data value='extract_http_request_entity'>EvidenceService._extract_http_request_entity</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t265">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t265"><data value='extract_network_endpoint_entity'>EvidenceService._extract_network_endpoint_entity</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t328">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t328"><data value='extract_url_entity'>EvidenceService._extract_url_entity</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t362">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t362"><data value='extract_file_entity'>EvidenceService._extract_file_entity</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t411">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t411"><data value='extract_process_entity'>EvidenceService._extract_process_entity</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t461">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t461"><data value='create_or_get_risk_entity'>EvidenceService._create_or_get_risk_entity</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t488">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t488"><data value='link_evidence_to_entity'>EvidenceService._link_evidence_to_entity</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t538">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t538"><data value='generate_evidence_id'>EvidenceService._generate_evidence_id</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t545">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t545"><data value='generate_entity_id'>EvidenceService._generate_entity_id</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t552">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t552"><data value='get_all_evidence_content_for_id'>EvidenceService._get_all_evidence_content_for_id</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t599">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t599"><data value='add_combined_type_specific_properties'>EvidenceService._add_combined_type_specific_properties</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t651">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t651"><data value='calculate_http_request_risk_score'>EvidenceService._calculate_http_request_risk_score</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t667">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t667"><data value='calculate_network_endpoint_risk_score'>EvidenceService._calculate_network_endpoint_risk_score</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t688">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t688"><data value='calculate_url_risk_score'>EvidenceService._calculate_url_risk_score</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t710">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t710"><data value='calculate_file_risk_score'>EvidenceService._calculate_file_risk_score</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t732">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t732"><data value='calculate_process_risk_score'>EvidenceService._calculate_process_risk_score</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t755">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t755"><data value='get_entity_by_id'>EvidenceService._get_entity_by_id</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t771">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t771"><data value='get_node_label_for_entity_type'>EvidenceService._get_node_label_for_entity_type</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t784">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t784"><data value='get_entity_label_from_type'>EvidenceService._get_entity_label_from_type</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t797">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t797"><data value='extract_dns_query_entity'>EvidenceService._extract_dns_query_entity</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t826">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t826"><data value='extract_http_response_entity'>EvidenceService._extract_http_response_entity</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t865">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t865"><data value='extract_user_entity'>EvidenceService._extract_user_entity</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t891">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t891"><data value='calculate_dns_query_risk_score'>EvidenceService._calculate_dns_query_risk_score</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t910">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t910"><data value='calculate_http_response_risk_score'>EvidenceService._calculate_http_response_risk_score</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t927">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html#t927"><data value='calculate_user_risk_score'>EvidenceService._calculate_user_risk_score</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html">app\services\evidence_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_evidence_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t24">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t24"><data value='init__'>VerdictService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t28">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t28"><data value='create_verdict'>VerdictService.create_verdict</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t69">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t69"><data value='add_verdict_to_alert'>VerdictService.add_verdict_to_alert</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t103">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t103"><data value='get_verdicts_by_alert'>VerdictService.get_verdicts_by_alert</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t122">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t122"><data value='get_latest_verdict'>VerdictService.get_latest_verdict</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t145">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t145"><data value='update_verdict'>VerdictService.update_verdict</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t199">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t199"><data value='get_verdict_by_id'>VerdictService.get_verdict_by_id</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t219">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t219"><data value='batch_apply_verdict'>VerdictService.batch_apply_verdict</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t253">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t253"><data value='apply_verdict_to_similar_alerts'>VerdictService.apply_verdict_to_similar_alerts</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t290">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html#t290"><data value='convert_to_response'>VerdictService._convert_to_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html">app\services\verdict_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_verdict_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html">app\utils\__init__.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t28">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t28"><data value='format'>JsonFormatter.format</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t64">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t64"><data value='init__'>AlertGraphLogger.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t69">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t69"><data value='setup_logger'>AlertGraphLogger._setup_logger</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t116">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t116"><data value='should_log_request'>AlertGraphLogger._should_log_request</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t133">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t133"><data value='log_with_extra'>AlertGraphLogger._log_with_extra</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t153">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t153"><data value='info'>AlertGraphLogger.info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t157">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t157"><data value='debug'>AlertGraphLogger.debug</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t162">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t162"><data value='warning'>AlertGraphLogger.warning</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t166">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t166"><data value='error'>AlertGraphLogger.error</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t177">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t177"><data value='critical'>AlertGraphLogger.critical</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t181">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t181"><data value='api_request'>AlertGraphLogger.api_request</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t201">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t201"><data value='api_response'>AlertGraphLogger.api_response</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t227">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t227"><data value='business_operation'>AlertGraphLogger.business_operation</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t246">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t246"><data value='database_error'>AlertGraphLogger.database_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t257">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t257"><data value='log_execution_time'>log_execution_time</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t263">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t263"><data value='decorator'>log_execution_time.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t265">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t265"><data value='wrapper'>log_execution_time.decorator.wrapper</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t299">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t299"><data value='log_database_operation'>log_database_operation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t313">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t313"><data value='cleanup_old_logs'>cleanup_old_logs</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t331">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t331"><data value='log_info'>log_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t336">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t336"><data value='log_error'>log_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t341">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html#t341"><data value='log_warning'>log_warning</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html">app\utils\logger.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_logger_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t17">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t17"><data value='get_current_tag'>TagManager.get_current_tag</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t37">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t37"><data value='resolve_tag'>TagManager.resolve_tag</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t72">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t72"><data value='add_tag_to_properties'>TagManager.add_tag_to_properties</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t88">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t88"><data value='add_tag_to_relationship_properties'>TagManager.add_tag_to_relationship_properties</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t107">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t107"><data value='validate_tag_consistency'>TagManager.validate_tag_consistency</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t133">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t133"><data value='create_tag_filter_query'>TagManager.create_tag_filter_query</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t149">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t149"><data value='get_cleanup_query'>TagManager.get_cleanup_query</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t169">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html#t169"><data value='is_testing_tag'>TagManager.is_testing_tag</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html">app\utils\tag_utils.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_tag_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2774</td>
                <td>2774</td>
                <td>0</td>
                <td class="right" data-ratio="0 2774">0%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-11 14:56 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
