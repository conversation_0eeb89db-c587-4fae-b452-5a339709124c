"""
设备信息数据模型

定义设备相关的Pydantic模型
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class DeviceBase(BaseModel):
    """设备基础模型"""
    device_id: str = Field(..., description="设备唯一标识")
    uuid: Optional[str] = Field(None, description="设备UUID")
    org: Optional[str] = Field(None, description="所属组织")
    ip: Optional[str] = Field(None, description="主要IP地址")
    hostname: Optional[str] = Field(None, description="主机名")
    mac: Optional[str] = Field(None, description="MAC地址")
    os_name: Optional[str] = Field(None, description="操作系统名称")
    count: Optional[int] = Field(1, description="告警数量统计")


class DeviceCreate(DeviceBase):
    """创建设备请求模型"""
    pass


class DeviceUpdate(BaseModel):
    """更新设备请求模型"""
    uuid: Optional[str] = None
    org: Optional[str] = None
    ip: Optional[str] = None
    hostname: Optional[str] = None
    mac: Optional[str] = None
    os_name: Optional[str] = None
    count: Optional[int] = None


class DeviceResponse(DeviceBase):
    """设备响应模型"""
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True 