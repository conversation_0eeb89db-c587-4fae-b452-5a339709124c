# AlertGraph 环境配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置

# ================================
# 应用基础配置
# ================================
APP_NAME=AlertGraph
APP_VERSION=0.1.0
DEBUG=false

# ================================
# Neo4j数据库配置
# ================================
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=firstpassword
NEO4J_DATABASE=neo4j

# ================================
# API配置
# ================================
API_PREFIX=/api/v1
DOCS_URL=/docs
REDOC_URL=/redoc

# ================================
# 分页配置
# ================================
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# ================================
# 日志配置
# ================================
LOG_LEVEL=INFO
# LOG_FILE=logs/app.log

# ================================
# 安全配置 (可选)
# ================================
# SECRET_KEY=your-secret-key-here
# ACCESS_TOKEN_EXPIRE_MINUTES=30

# ================================
# 其他配置
# ================================
# CORS_ORIGINS=http://localhost:3000,http://localhost:8080
# TIMEZONE=Asia/Shanghai 