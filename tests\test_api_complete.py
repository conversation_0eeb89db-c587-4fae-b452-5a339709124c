#!/usr/bin/env python3
"""
AlertGraph API 完整测试方案

测试所有API端点的功能，确保data_tag正确传递到所有节点和关系中。
测试流程：
1. 系统健康检查
2. 创建告警（单个和批量）
3. 查询告警
4. 更新告警
5. 创建研判记录
6. 为告警添加研判
7. 查询研判记录
8. 获取告警相关实体
9. 验证data_tag传递
10. 清理测试数据

所有测试数据都使用 data_tag="api_test" 标识
"""

import json
import requests
import time
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from test_config import TestConfig, TestDataGenerator, TestValidator, TestReporter


class AlertGraphAPITester:
    """AlertGraph API 测试器"""

    def __init__(self, base_url: str = None):
        self.base_url = base_url or TestConfig.API_BASE_URL
        self.api_base = f"{self.base_url}/api/{TestConfig.API_VERSION}"
        self.test_tag = TestConfig.TEST_TAG
        self.session = requests.Session()
        self.session.headers.update(
            {
                "Content-Type": "application/json",
                "X-Testing": "true",  # 标识测试环境
            }
        )

        # 存储测试过程中创建的资源ID
        self.created_alerts = []
        self.created_verdicts = []
        self.test_markers = []  # 用于清理的测试标识符

        # 测试报告器
        self.reporter = TestReporter()

        # 测试统计
        self.test_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
        }

    def log(self, message: str, level: str = "INFO"):
        """记录测试日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")

    def make_request(
        self, method: str, endpoint: str, data: Dict = None, params: Dict = None
    ) -> Dict:
        """发送HTTP请求"""
        url = f"{self.api_base}{endpoint}"
        start_time = time.time()

        # 更新统计
        self.test_stats["total_requests"] += 1

        try:
            if method.upper() == "GET":
                response = self.session.get(
                    url, params=params, timeout=TestConfig.TEST_TIMEOUT
                )
            elif method.upper() == "POST":
                response = self.session.post(
                    url, json=data, params=params, timeout=TestConfig.TEST_TIMEOUT
                )
            elif method.upper() == "PUT":
                response = self.session.put(
                    url, json=data, params=params, timeout=TestConfig.TEST_TIMEOUT
                )
            elif method.upper() == "DELETE":
                response = self.session.delete(
                    url, params=params, timeout=TestConfig.TEST_TIMEOUT
                )
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            duration = time.time() - start_time
            success = 200 <= response.status_code < 300

            # 更新统计
            if success:
                self.test_stats["successful_requests"] += 1
            else:
                self.test_stats["failed_requests"] += 1

            # 记录请求
            self.log(
                f"{method.upper()} {endpoint} -> {response.status_code} ({duration:.2f}s)"
            )

            if response.status_code >= 400:
                self.log(f"请求失败: {response.text}", "ERROR")

            return {
                "status_code": response.status_code,
                "data": response.json() if response.content else None,
                "success": success,
                "duration": duration,
            }

        except Exception as e:
            duration = time.time() - start_time
            self.test_stats["failed_requests"] += 1
            self.log(f"请求异常: {str(e)}", "ERROR")
            return {
                "status_code": 0,
                "data": None,
                "success": False,
                "error": str(e),
                "duration": duration,
            }

    def test_system_health(self) -> bool:
        """测试系统健康状态"""
        self.log("=== 测试系统健康状态 ===")

        # 测试系统健康检查
        result = self.make_request("GET", "/system/health")
        if not result["success"]:
            self.log("系统健康检查失败", "ERROR")
            return False

        health_data = result["data"]
        self.log(f"系统状态: {health_data.get('status', 'unknown')}")

        # 测试系统统计
        result = self.make_request("GET", "/system/stats")
        if not result["success"]:
            self.log("系统统计获取失败", "ERROR")
            return False

        stats = result["data"]
        self.log(f"当前告警数量: {stats.get('alert_count', 0)}")
        self.log(f"当前实体数量: {stats.get('entity_count', 0)}")

        return True

    def load_test_alerts(self) -> List[Dict]:
        """加载测试告警数据"""
        try:
            # 尝试加载真实数据格式
            assets_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "assets",
                "data",
                "api_format_alerts.json",
            )
            if os.path.exists(assets_path):
                with open(assets_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    base_alerts = data["alerts"][: TestConfig.TEST_ALERT_COUNT]

                # 使用数据生成器处理真实数据
                test_alerts = []
                for i, base_alert in enumerate(base_alerts):
                    test_alert = TestDataGenerator.generate_test_alert(
                        i + 1, base_alert
                    )
                    test_alerts.append(test_alert)
                    self.test_markers.append(test_alert["alert_detail_id"])

                self.log(f"加载了 {len(test_alerts)} 个基于真实格式的测试告警")
                return test_alerts
            else:
                # 如果没有真实数据，生成默认测试数据
                self.log("未找到真实数据文件，生成默认测试数据", "WARN")
                test_alerts = TestDataGenerator.generate_batch_alerts(
                    TestConfig.TEST_ALERT_COUNT
                )
                for alert in test_alerts:
                    self.test_markers.append(alert["alert_detail_id"])

                self.log(f"生成了 {len(test_alerts)} 个默认测试告警")
                return test_alerts

        except Exception as e:
            self.log(f"加载测试数据失败: {str(e)}", "ERROR")
            # 生成备用测试数据
            test_alerts = TestDataGenerator.generate_batch_alerts(3)  # 最少3个告警
            for alert in test_alerts:
                self.test_markers.append(alert["alert_detail_id"])
            return test_alerts

    def test_create_single_alert(self, alert_data: Dict) -> str:
        """测试创建单个告警"""
        self.log(f"创建告警: {alert_data['alert_detail_id']}")

        result = self.make_request("POST", "/alerts", alert_data)
        if not result["success"]:
            self.log(
                f"创建告警失败: {result.get('data', {}).get('detail', 'Unknown error')}",
                "ERROR",
            )
            return None

        alert_response = result["data"]
        alert_id = alert_response["vid"]
        self.created_alerts.append(alert_id)

        self.log(f"告警创建成功: {alert_id}")
        return alert_id

    def test_batch_create_alerts(self, alerts_data: List[Dict]) -> List[str]:
        """测试批量创建告警"""
        self.log(f"批量创建 {len(alerts_data)} 个告警")

        result = self.make_request("POST", "/alerts/batch", alerts_data)
        if not result["success"]:
            self.log(
                f"批量创建告警失败: {result.get('data', {}).get('detail', 'Unknown error')}",
                "ERROR",
            )
            return []

        batch_response = result["data"]
        success_count = batch_response["success_count"]
        failed_count = batch_response["failed_count"]
        created_ids = batch_response["created_ids"]

        self.log(f"批量创建结果: 成功 {success_count}, 失败 {failed_count}")

        if failed_count > 0:
            self.log(f"失败详情: {batch_response['errors']}", "WARN")

        self.created_alerts.extend(created_ids)
        return created_ids

    def test_query_alerts(self) -> bool:
        """测试查询告警"""
        self.log("=== 测试查询告警 ===")

        # 按时间范围查询
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)

        params = {
            "start_time": start_time.isoformat() + "Z",
            "end_time": end_time.isoformat() + "Z",
        }

        result = self.make_request("GET", "/alerts", params=params)
        if not result["success"]:
            self.log("查询告警失败", "ERROR")
            return False

        alerts = result["data"]
        self.log(f"查询到 {len(alerts)} 个告警")

        # 测试获取单个告警详情
        if self.created_alerts:
            alert_id = self.created_alerts[0]
            result = self.make_request("GET", f"/alerts/{alert_id}")
            if result["success"]:
                alert_detail = result["data"]
                self.log(f"获取告警详情成功: {alert_detail['alert_name']}")
                self.log(f"告警data_tag: {alert_detail.get('data_tag', 'None')}")
            else:
                self.log("获取告警详情失败", "ERROR")
                return False

        return True

    def test_update_alert(self) -> bool:
        """测试更新告警"""
        if not self.created_alerts:
            self.log("没有可更新的告警", "WARN")
            return True

        self.log("=== 测试更新告警 ===")

        alert_id = self.created_alerts[0]
        update_data = {
            "status": "InProgress",
            "comment": f"API测试更新 - {datetime.now().isoformat()}",
        }

        result = self.make_request("PUT", f"/alerts/{alert_id}", update_data)
        if not result["success"]:
            self.log("更新告警失败", "ERROR")
            return False

        updated_alert = result["data"]
        self.log(f"告警更新成功: 状态 -> {updated_alert['status']}")

        return True

    def test_create_verdict(self) -> str:
        """测试创建独立研判记录"""
        self.log("=== 测试创建研判记录 ===")

        verdict_data = {
            "type": "人工研判",
            "label": 1,
            "reason": f"API测试研判 - {datetime.now().isoformat()}",
            "commiter": "api_tester",
            "comment": f"测试标识: {self.test_tag}",
            "data_tag": self.test_tag,  # 添加data_tag字段
        }

        result = self.make_request("POST", "/verdicts", verdict_data)
        if not result["success"]:
            self.log("创建研判记录失败", "ERROR")
            return None

        verdict_response = result["data"]
        verdict_id = verdict_response["verdict_id"]
        self.created_verdicts.append(verdict_id)

        self.log(f"研判记录创建成功: {verdict_id}")
        return verdict_id

    def test_add_verdict_to_alert(self) -> bool:
        """测试为告警添加研判"""
        if not self.created_alerts:
            self.log("没有可添加研判的告警", "WARN")
            return True

        self.log("=== 测试为告警添加研判 ===")

        alert_id = self.created_alerts[0]
        verdict_data = {
            "type": "人工研判",
            "label": 2,
            "reason": f"API测试告警研判 - {datetime.now().isoformat()}",
            "commiter": "api_tester",
            "comment": f"告警研判测试 - {self.test_tag}",
            "data_tag": self.test_tag,  # 添加data_tag字段
        }

        result = self.make_request("POST", f"/alerts/{alert_id}/verdicts", verdict_data)
        if not result["success"]:
            self.log("为告警添加研判失败", "ERROR")
            return False

        verdict_response = result["data"]
        self.log(f"告警研判添加成功: {verdict_response['verdict_id']}")

        # 测试获取告警的研判记录
        result = self.make_request("GET", f"/alerts/{alert_id}/verdicts")
        if result["success"]:
            verdicts = result["data"]
            self.log(f"告警 {alert_id} 有 {len(verdicts)} 条研判记录")

        # 测试获取最新研判
        result = self.make_request("GET", f"/alerts/{alert_id}/verdicts/latest")
        if result["success"]:
            latest_verdict = result["data"]
            self.log(f"最新研判: {latest_verdict['reason']}")

        return True

    def test_get_alert_entities(self) -> bool:
        """测试获取告警相关实体"""
        if not self.created_alerts:
            self.log("没有可查询实体的告警", "WARN")
            return True

        self.log("=== 测试获取告警实体 ===")

        alert_id = self.created_alerts[0]
        result = self.make_request("GET", f"/alerts/{alert_id}/entities")
        if not result["success"]:
            self.log("获取告警实体失败", "ERROR")
            return False

        entities_response = result["data"]
        entities = entities_response["entities"]
        total_count = entities_response["total_count"]

        self.log(f"告警 {alert_id} 关联 {total_count} 个实体")

        for entity in entities[:3]:  # 只显示前3个实体
            self.log(
                f"实体: {entity.get('name', 'Unknown')} ({entity.get('entity_type', 'Unknown')})"
            )

        return True

    def cleanup_test_data(self) -> bool:
        """清理测试数据 - 统一使用data_tag方式"""
        self.log("=== 清理测试数据 ===")

        # 使用data_tag清理数据（推荐方式）
        params = {
            "test_markers": self.test_markers,  # 保持兼容性
            "data_tag": self.test_tag,  # 明确指定data_tag
        }

        result = self.make_request("DELETE", "/system/test/clean", params=params)
        if not result["success"]:
            self.log("清理测试数据失败", "ERROR")
            return False

        cleanup_response = result["data"]
        self.log(f"清理结果: {cleanup_response.get('message', 'Unknown')}")

        cleaned_nodes = cleanup_response.get("cleaned_nodes", {})
        for node_type, count in cleaned_nodes.items():
            self.log(f"清理 {node_type}: {count} 个节点")

        return True

    def run_complete_test(self) -> bool:
        """运行完整测试流程"""
        self.log("开始 AlertGraph API 完整测试")
        self.log(f"测试标识: {self.test_tag}")
        self.log(f"API地址: {self.api_base}")

        try:
            # 1. 系统健康检查
            if not self.test_system_health():
                return False

            # 2. 加载测试数据
            test_alerts = self.load_test_alerts()
            if not test_alerts:
                return False

            # 3. 创建单个告警
            single_alert = test_alerts[0]
            alert_id = self.test_create_single_alert(single_alert)
            if not alert_id:
                return False

            # 4. 批量创建告警
            batch_alerts = test_alerts[1:3]  # 取2个告警进行批量测试
            batch_ids = self.test_batch_create_alerts(batch_alerts)

            # 5. 查询告警
            if not self.test_query_alerts():
                return False

            # 6. 更新告警
            if not self.test_update_alert():
                return False

            # 7. 创建研判记录
            verdict_id = self.test_create_verdict()

            # 8. 为告警添加研判
            if not self.test_add_verdict_to_alert():
                return False

            # 9. 获取告警实体
            if not self.test_get_alert_entities():
                return False

            # 10. 清理测试数据
            if not self.cleanup_test_data():
                self.log("清理失败，请手动清理测试数据", "WARN")

            self.log("=== API测试完成 ===")
            self.log("所有测试通过！")
            return True

        except Exception as e:
            self.log(f"测试过程中发生异常: {str(e)}", "ERROR")
            return False


def main():
    """主函数"""
    tester = AlertGraphAPITester()
    success = tester.run_complete_test()

    if success:
        print("\n✅ AlertGraph API 测试成功完成！")
        exit(0)
    else:
        print("\n❌ AlertGraph API 测试失败！")
        exit(1)


if __name__ == "__main__":
    main()
