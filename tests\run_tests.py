#!/usr/bin/env python3
"""
AlertGraph API 测试运行器

简化的测试运行脚本，用于快速执行API测试
"""

import os
import sys
import json
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入测试模块
from tests.test_api_complete import AlertGraphAPITester


def check_server_status(base_url: str = "http://localhost:8000") -> bool:
    """检查服务器状态"""
    import requests
    try:
        response = requests.get(f"{base_url}/api/v1/system/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 服务器状态: {health_data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ 服务器健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        print("请确保AlertGraph服务正在运行在 http://localhost:8000")
        return False


def run_api_tests():
    """运行API测试"""
    print("🚀 AlertGraph API 测试开始")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server_status():
        print("\n❌ 测试终止：服务器不可用")
        return False
    
    # 创建测试器实例
    tester = AlertGraphAPITester()
    
    # 运行测试
    start_time = time.time()
    success = tester.run_complete_test()
    end_time = time.time()
    
    # 输出测试结果
    duration = end_time - start_time
    print("\n" + "=" * 50)
    print("📊 测试结果统计")
    print("=" * 50)
    print(f"总耗时: {duration:.2f} 秒")
    print(f"总请求数: {tester.test_stats['total_requests']}")
    print(f"成功请求: {tester.test_stats['successful_requests']}")
    print(f"失败请求: {tester.test_stats['failed_requests']}")
    
    if tester.test_stats['total_requests'] > 0:
        success_rate = (tester.test_stats['successful_requests'] / tester.test_stats['total_requests']) * 100
        print(f"成功率: {success_rate:.1f}%")
    
    # 生成测试报告
    try:
        report_file = tester.reporter.save_report()
        print(f"📄 详细报告: {report_file}")
    except Exception as e:
        print(f"⚠️  报告生成失败: {str(e)}")
    
    return success


def main():
    """主函数"""
    try:
        success = run_api_tests()
        
        if success:
            print("\n🎉 所有API测试通过！")
            sys.exit(0)
        else:
            print("\n💥 API测试失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试运行异常: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
