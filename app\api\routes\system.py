"""
系统管理API路由

提供数据库状态查看、测试环境管理、系统监控等管理功能
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from app.database.connection import Neo4jConnection
from app.api.dependencies import get_neo4j_connection
from app.utils.logger import logger
# 移除了复杂的测试管理器依赖

router = APIRouter(prefix="/system", tags=["系统管理"])


@router.get("/health", response_model=Dict[str, Any])
def get_system_health(connection: Neo4jConnection = Depends(get_neo4j_connection)):
    """获取系统健康状态"""
    try:
        health_result = connection.health_check()
        return {
            "system": "AlertGraph",
            "status": "healthy",
            "timestamp": health_result.get("timestamp"),
            "database": health_result,
            "version": "0.1.0",
        }
    except Exception as e:
        logger.error("系统健康检查失败", error=e)
        raise HTTPException(status_code=500, detail=f"系统健康检查失败: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
def get_system_stats(connection: Neo4jConnection = Depends(get_neo4j_connection)):
    """获取系统统计信息"""
    try:
        # 获取各种节点数量统计
        alert_query = "MATCH (a:AlertDetail) RETURN count(a) as count"
        device_query = "MATCH (d:Device) RETURN count(d) as count"
        entity_query = "MATCH (e:Entity) RETURN count(e) as count"
        evidence_query = "MATCH (ev:Evidence) RETURN count(ev) as count"

        alert_result = connection.execute_read_transaction(alert_query)
        device_result = connection.execute_read_transaction(device_query)
        entity_result = connection.execute_read_transaction(entity_query)
        evidence_result = connection.execute_read_transaction(evidence_query)

        alert_count = alert_result[0]["count"] if alert_result else 0
        device_count = device_result[0]["count"] if device_result else 0
        entity_count = entity_result[0]["count"] if entity_result else 0
        evidence_count = evidence_result[0]["count"] if evidence_result else 0

        from datetime import datetime

        return {
            "alert_count": alert_count,
            "device_count": device_count,
            "entity_count": entity_count,
            "evidence_count": evidence_count,
            "total_nodes": alert_count + device_count + entity_count + evidence_count,
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        logger.error("获取系统统计失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取系统统计失败: {str(e)}")


@router.get("/database/status", response_model=Dict[str, Any])
def get_database_status(connection: Neo4jConnection = Depends(get_neo4j_connection)):
    """获取数据库状态信息"""
    try:
        health_result = connection.health_check()

        return {
            "database": "Neo4j",
            "connection": "active"
            if health_result.get("status") == "healthy"
            else "inactive",
            "statistics": health_result,
            "timestamp": health_result.get("timestamp"),
        }
    except Exception as e:
        logger.error("获取数据库状态失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取数据库状态失败: {str(e)}")


@router.get("/database/nodes", response_model=Dict[str, Any])
def get_node_statistics(
    label: Optional[str] = Query(None, description="节点标签"),
    connection: Neo4jConnection = Depends(get_neo4j_connection),
):
    """获取节点统计信息"""
    try:
        if label:
            query = f"MATCH (n:{label}) RETURN count(n) as count"
            result = connection.execute_read_transaction(query)
            count = result[0]["count"] if result else 0
            return {"label": label, "count": count}
        else:
            query = "MATCH (n) RETURN labels(n) as labels, count(n) as count"
            result = connection.execute_read_transaction(query)
            node_stats = {}
            total = 0
            for record in result:
                labels = record["labels"]
                count = record["count"]
                for label in labels:
                    node_stats[label] = node_stats.get(label, 0) + count
                total += count

            return {"node_statistics": node_stats, "total_nodes": total}
    except Exception as e:
        logger.error("获取节点统计失败", error=e, label=label)
        raise HTTPException(status_code=500, detail=f"获取节点统计失败: {str(e)}")


@router.get("/database/relationships", response_model=Dict[str, Any])
def get_relationship_statistics(
    rel_type: Optional[str] = Query(None, description="关系类型"),
    connection: Neo4jConnection = Depends(get_neo4j_connection),
):
    """获取关系统计信息"""
    try:
        if rel_type:
            query = f"MATCH ()-[r:{rel_type}]-() RETURN count(r) as count"
            result = connection.execute_read_transaction(query)
            count = result[0]["count"] if result else 0
            return {"relationship_type": rel_type, "count": count}
        else:
            query = "MATCH ()-[r]-() RETURN type(r) as rel_type, count(r) as count"
            result = connection.execute_read_transaction(query)
            rel_stats = {}
            total = 0
            for record in result:
                rel_type = record["rel_type"]
                count = record["count"]
                rel_stats[rel_type] = count
                total += count

            return {"relationship_statistics": rel_stats, "total_relationships": total}
    except Exception as e:
        logger.error("获取关系统计失败", error=e, rel_type=rel_type)
        raise HTTPException(status_code=500, detail=f"获取关系统计失败: {str(e)}")


# 测试环境管理API（仅在测试模式下可用）
@router.post("/test/reset-database", response_model=Dict[str, Any])
def reset_test_database(connection: Neo4jConnection = Depends(get_neo4j_connection)):
    """重置测试数据库（仅测试环境）"""
    import os

    if not os.getenv("TESTING"):
        raise HTTPException(status_code=403, detail="此功能仅在测试环境下可用")

    try:
        # 简化的数据库重置
        query = "MATCH (n) DETACH DELETE n"
        connection.execute_write_transaction(query)

        return {
            "success": True,
            "message": "测试数据库重置完成",
            "timestamp": "2024-01-01T00:00:00",
        }
    except Exception as e:
        logger.error("重置测试数据库失败", error=e)
        raise HTTPException(status_code=500, detail=f"重置测试数据库失败: {str(e)}")


@router.delete("/test/clean", response_model=Dict[str, Any])
def clean_test_data(
    request: Request,
    labels: Optional[List[str]] = Query(None, description="要清理的节点标签"),
    test_markers: Optional[List[str]] = Query(None, description="测试标识符列表"),
    connection: Neo4jConnection = Depends(get_neo4j_connection),
):
    """清理测试数据（仅测试环境）"""
    import os

    # 检查测试环境：环境变量或请求头
    is_testing = os.getenv("TESTING") == "true" or (
        request and request.headers.get("X-Testing") == "true"
    )

    if not is_testing:
        raise HTTPException(status_code=403, detail="此功能仅在测试环境下可用")

    try:
        cleaned_nodes = {}
        cleaned_relationships = 0

        if test_markers:
            # 基于测试标识清理数据（推荐方式）
            from datetime import datetime

            for marker in test_markers:
                # 查找包含测试标识的节点
                search_queries = [
                    # 查找comment字段包含测试标识的节点
                    f"MATCH (n) WHERE n.comment CONTAINS '{marker}' OR n.raw_data CONTAINS '{marker}' OR n.description CONTAINS '{marker}' RETURN n, labels(n) as node_labels",
                    # 查找name字段包含测试标识的节点
                    f"MATCH (n) WHERE n.name CONTAINS '{marker}' RETURN n, labels(n) as node_labels",
                    # 查找properties中包含测试标识的节点
                    f"MATCH (n) WHERE ANY(key IN keys(n) WHERE toString(n[key]) CONTAINS '{marker}') RETURN n, labels(n) as node_labels",
                    # 查找alert_detail_id包含测试标识的节点
                    f"MATCH (n) WHERE n.alert_detail_id CONTAINS '{marker}' RETURN n, labels(n) as node_labels",
                ]

                # 收集要删除的节点
                nodes_to_delete = []
                for query in search_queries:
                    result = connection.execute_read_transaction(query)
                    for record in result:
                        node = record["n"]
                        labels = record["node_labels"]
                        if (
                            node.get("vid")
                            or node.get("entity_id")
                            or node.get("verdict_id")
                            or node.get("device_id")
                        ):
                            nodes_to_delete.append(
                                {
                                    "id": node.get("vid")
                                    or node.get("entity_id")
                                    or node.get("verdict_id")
                                    or node.get("device_id"),
                                    "labels": labels,
                                    "properties": dict(node),
                                }
                            )

                # 删除找到的节点及其关系
                for node in nodes_to_delete:
                    node_id = node["id"]
                    primary_label = node["labels"][0] if node["labels"] else "Node"

                    # 简化删除：直接删除节点和关系
                    delete_query = f"""
                    MATCH (n)
                    WHERE n.vid = '{node_id}' OR n.entity_id = '{node_id}' OR n.verdict_id = '{node_id}' OR n.device_id = '{node_id}'
                    DETACH DELETE n
                    RETURN count(n) as deleted_count
                    """

                    delete_result = connection.execute_write_transaction(delete_query)

                    # 统计删除的节点
                    if delete_result and delete_result[0].get("deleted_count", 0) > 0:
                        if primary_label not in cleaned_nodes:
                            cleaned_nodes[primary_label] = 0
                        cleaned_nodes[primary_label] += delete_result[0].get(
                            "deleted_count", 0
                        )

            # 额外清理：基于data_tag清理遗留的测试节点
            test_data_tags = ["testing", "development", "unit_test", "integration"]
            for tag in test_data_tags:
                tag_cleanup_query = f"""
                MATCH (n)
                WHERE n.data_tag = '{tag}'
                DETACH DELETE n
                RETURN count(n) as deleted_count, collect(DISTINCT labels(n)[0]) as deleted_labels
                """

                tag_result = connection.execute_write_transaction(tag_cleanup_query)
                if tag_result and tag_result[0].get("deleted_count", 0) > 0:
                    deleted_count = tag_result[0]["deleted_count"]
                    deleted_labels = tag_result[0]["deleted_labels"]

                    for label in deleted_labels:
                        if label not in cleaned_nodes:
                            cleaned_nodes[label] = 0
                        cleaned_nodes[label] += deleted_count // len(
                            deleted_labels
                        )  # 平均分配

            message = f"基于测试标识清理完成: {test_markers}"

        elif labels:
            # 按标签清理（原有逻辑）
            for label in labels:
                count_query = f"MATCH (n:{label}) RETURN count(n) as count"
                count_result = connection.execute_read_transaction(count_query)
                node_count = count_result[0]["count"] if count_result else 0

                if node_count > 0:
                    query = f"MATCH (n:{label}) DETACH DELETE n"
                    connection.execute_write_transaction(query)
                    cleaned_nodes[label] = node_count

            message = f"按标签清理完成: {labels}"

        else:
            # 清理所有数据（危险操作）
            count_query = "MATCH (n) RETURN count(n) as count"
            count_result = connection.execute_read_transaction(count_query)
            total_nodes = count_result[0]["count"] if count_result else 0

            query = "MATCH (n) DETACH DELETE n"
            connection.execute_write_transaction(query)
            cleaned_nodes["All"] = total_nodes
            message = "清理所有测试数据完成"

        return {
            "success": True,
            "message": message,
            "cleaned_nodes": cleaned_nodes,
            "cleaned_relationships": cleaned_relationships,
            "test_markers": test_markers,
            "cleaned_labels": labels,
            "timestamp": datetime.now().isoformat(),
        }
    except Exception as e:
        logger.error(
            "清理测试数据失败", error=e, labels=labels, test_markers=test_markers
        )
        raise HTTPException(status_code=500, detail=f"清理测试数据失败: {str(e)}")


@router.get("/test/status", response_model=Dict[str, Any])
def get_test_environment_status(
    connection: Neo4jConnection = Depends(get_neo4j_connection),
):
    """获取测试环境状态（仅测试环境）"""
    import os

    if not os.getenv("TESTING"):
        raise HTTPException(status_code=403, detail="此功能仅在测试环境下可用")

    try:
        health_result = connection.health_check()

        return {
            "test_environment": True,
            "database_status": health_result,
            "testing_enabled": bool(os.getenv("TESTING")),
            "log_level": os.getenv("LOG_LEVEL", "INFO"),
        }
    except Exception as e:
        logger.error("获取测试环境状态失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取测试环境状态失败: {str(e)}")


@router.get("/logs/recent", response_model=Dict[str, Any])
def get_recent_logs(
    limit: int = Query(100, description="日志条数限制", ge=1, le=1000),
    level: Optional[str] = Query(None, description="日志级别"),
):
    """获取最近的日志记录"""
    try:
        # 这里应该从日志存储中读取，暂时返回示例数据
        logs = [
            {
                "timestamp": "2024-01-01T12:00:00",
                "level": "INFO",
                "message": "API请求处理完成",
                "service": "alertgraph",
                "module": "api",
            }
        ]

        return {
            "logs": logs[:limit],
            "total_count": len(logs),
            "limit": limit,
            "level_filter": level,
        }
    except Exception as e:
        logger.error("获取日志记录失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取日志记录失败: {str(e)}")


@router.get("/performance/metrics", response_model=Dict[str, Any])
def get_performance_metrics():
    """获取性能指标"""
    try:
        # 这里应该从监控系统获取实际指标
        metrics = {
            "api_response_time": {"average": 150, "p95": 300, "p99": 500},
            "database_query_time": {"average": 50, "p95": 100, "p99": 200},
            "memory_usage": {"used": 512, "total": 2048, "percentage": 25},
            "request_count": {"total": 1000, "successful": 950, "failed": 50},
        }

        return {
            "metrics": metrics,
            "timestamp": "2024-01-01T12:00:00",
            "collection_interval": "1m",
        }
    except Exception as e:
        logger.error("获取性能指标失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")
