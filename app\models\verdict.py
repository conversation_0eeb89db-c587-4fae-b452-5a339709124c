"""
研判记录数据模型

包含研判记录的Pydantic模型定义
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from enum import IntEnum


class VerdictType(IntEnum):
    """研判类型枚举"""
    AI = 1          # AI研判
    MANUAL = 2      # 人工研判


class VerdictLabel(IntEnum):
    """研判标签枚举"""
    FALSE_POSITIVE = 1      # 误报
    CONFIRMED_ATTACK = 2    # 确认攻击
    SUSPICIOUS = 3          # 可疑
    INSUFFICIENT_DATA = 4   # 数据不足


class VerdictCreate(BaseModel):
    """创建研判记录请求模型"""
    type_id: VerdictType = Field(..., description="研判类型 (1-AI, 2-人工)")
    label: VerdictLabel = Field(..., description="研判标签 (1-误报, 2-确认攻击, 3-可疑, 4-数据不足)")
    reason: str = Field(..., min_length=1, max_length=1000, description="研判理由")
    commiter: str = Field(..., min_length=1, max_length=100, description="研判提交者")
    comment: Optional[str] = Field(None, max_length=500, description="研判评论")
    
    class Config:
        use_enum_values = True
        json_schema_extra = {
            "example": {
                "type_id": 2,
                "label": 1,
                "reason": "经过分析，此告警为系统正常行为触发的误报",
                "commiter": "analyst_001",
                "comment": "建议调整检测规则阈值"
            }
        }


class VerdictUpdate(BaseModel):
    """更新研判记录请求模型"""
    label: Optional[VerdictLabel] = Field(None, description="研判标签")
    reason: Optional[str] = Field(None, min_length=1, max_length=1000, description="研判理由")
    comment: Optional[str] = Field(None, max_length=500, description="研判评论")
    
    class Config:
        use_enum_values = True


class VerdictResponse(BaseModel):
    """研判记录响应模型"""
    vid: str = Field(..., description="图数据库节点ID")
    verdict_id: str = Field(..., description="研判记录唯一ID")
    type_id: int = Field(..., description="研判类型")
    type: str = Field(..., description="研判类型名称")
    label: int = Field(..., description="研判标签")
    label_name: str = Field(..., description="研判标签名称")
    reason: str = Field(..., description="研判理由")
    commiter: str = Field(..., description="研判提交者")
    comment: Optional[str] = Field(None, description="研判评论")
    time: datetime = Field(..., description="研判时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 关联信息
    alert_detail_id: Optional[str] = Field(None, description="关联的告警ID")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "vid": "verdict_12345",
                "verdict_id": "VD-20241209-001",
                "type_id": 2,
                "type": "人工研判",
                "label": 1,
                "label_name": "误报",
                "reason": "经过分析，此告警为系统正常行为触发的误报",
                "commiter": "analyst_001",
                "comment": "建议调整检测规则阈值",
                "time": "2024-12-09T16:30:00",
                "created_at": "2024-12-09T16:30:00",
                "updated_at": "2024-12-09T16:30:00",
                "alert_detail_id": "AD-20241209-001"
            }
        }


class VerdictQuery(BaseModel):
    """研判记录查询参数"""
    alert_detail_id: Optional[str] = Field(None, description="告警ID")
    type_id: Optional[VerdictType] = Field(None, description="研判类型")
    label: Optional[VerdictLabel] = Field(None, description="研判标签")
    commiter: Optional[str] = Field(None, description="研判提交者")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    
    # 分页参数
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")
    
    class Config:
        use_enum_values = True


class VerdictListResponse(BaseModel):
    """研判记录列表响应"""
    items: list[VerdictResponse] = Field(..., description="研判记录列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class VerdictStatistics(BaseModel):
    """研判统计信息"""
    total_verdicts: int = Field(..., description="总研判数")
    ai_verdicts: int = Field(..., description="AI研判数")
    manual_verdicts: int = Field(..., description="人工研判数")
    false_positive_rate: float = Field(..., description="误报率")
    confirmed_attack_rate: float = Field(..., description="确认攻击率")
    top_committers: list[dict] = Field(..., description="研判员排行")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_verdicts": 1000,
                "ai_verdicts": 600,
                "manual_verdicts": 400,
                "false_positive_rate": 0.25,
                "confirmed_attack_rate": 0.45,
                "top_committers": [
                    {"commiter": "analyst_001", "count": 150},
                    {"commiter": "ai_system", "count": 600}
                ]
            }
        }


# 工具函数
def get_verdict_type_name(type_id: int) -> str:
    """获取研判类型名称"""
    type_mapping = {
        VerdictType.AI: "AI研判",
        VerdictType.MANUAL: "人工研判"
    }
    return type_mapping.get(type_id, "未知类型")


def get_verdict_label_name(label: int) -> str:
    """获取研判标签名称"""
    label_mapping = {
        VerdictLabel.FALSE_POSITIVE: "误报",
        VerdictLabel.CONFIRMED_ATTACK: "确认攻击", 
        VerdictLabel.SUSPICIOUS: "可疑",
        VerdictLabel.INSUFFICIENT_DATA: "数据不足"
    }
    return label_mapping.get(label, "未知标签") 