"""
告警相关API路由
"""

from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from app.services import AlertService
from app.api.dependencies import get_alert_service
from app.utils.logger import logger
from app.models.alert import (
    AlertDetailCreate,
    AlertDetailUpdate,
    AlertDetailResponse,
    AlertSearchFilters,
    BatchCreateResponse
)
from app.models.base import AlertStatus, ResponseModel

router = APIRouter(prefix="/alerts", tags=["告警管理"])


@router.post("/", response_model=AlertDetailResponse)
def create_alert(
    alert_data: AlertDetailCreate,
    alert_service: AlertService = Depends(get_alert_service)
):
    """创建告警细节"""
    try:
        return alert_service.create_alert_detail(alert_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/batch", response_model=BatchCreateResponse)
def batch_create_alerts(
    alerts: List[AlertDetailCreate],
    alert_service: AlertService = Depends(get_alert_service)
):
    """批量创建告警细节"""
    try:
        # 简化API日志 - 移除重复记录，服务层已有记录
        result = alert_service.batch_create_alerts(alerts)
        return result
    except Exception as e:
        logger.error(f"API批量创建告警失败", error=e, alert_count=len(alerts))
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{alert_id}", response_model=AlertDetailResponse)
def get_alert(
    alert_id: str,
    alert_service: AlertService = Depends(get_alert_service)
):
    """获取告警细节"""
    try:
        alert = alert_service.get_alert_by_id(alert_id)
        if not alert:
            raise HTTPException(status_code=404, detail="告警不存在")
        return alert
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{alert_id}", response_model=AlertDetailResponse)
def update_alert(
    alert_id: str,
    update_data: AlertDetailUpdate,
    alert_service: AlertService = Depends(get_alert_service)
):
    """更新告警细节"""
    try:
        alert = alert_service.update_alert_fields(alert_id, update_data)
        if not alert:
            raise HTTPException(status_code=404, detail="告警不存在")
        return alert
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{alert_id}/status", response_model=ResponseModel)
def update_alert_status(
    alert_id: str,
    status: AlertStatus,
    alert_service: AlertService = Depends(get_alert_service)
):
    """更新告警状态"""
    try:
        success = alert_service.update_alert_status(alert_id, status)
        if not success:
            raise HTTPException(status_code=404, detail="告警不存在")
        return ResponseModel(success=True, message="状态更新成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=List[AlertDetailResponse])
def get_alerts_by_time_range(
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    alert_service: AlertService = Depends(get_alert_service)
):
    """按时间范围查询告警"""
    try:
        if not start_time or not end_time:
            raise HTTPException(status_code=400, detail="开始时间和结束时间都是必需的")
        
        alerts = alert_service.get_alerts_by_time_range(start_time, end_time)
        return alerts
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/device/{device_id}", response_model=List[AlertDetailResponse])
def get_alerts_by_device(
    device_id: str,
    alert_service: AlertService = Depends(get_alert_service)
):
    """获取设备的告警列表"""
    try:
        alerts = alert_service.get_alerts_by_device(device_id)
        return alerts
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{alert_id}/entities")
def get_alert_entities(
    alert_id: str,
    alert_service: AlertService = Depends(get_alert_service)
):
    """获取告警相关的风险实体（通过证据节点关联）"""
    try:
        entities = alert_service.get_alert_related_entities(alert_id)
        return {
            "alert_id": alert_id,
            "entities": entities,
            "total_count": len(entities)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 