#!/usr/bin/env python3
"""
AlertGraph API 独立测试脚本

仅依赖Python标准库的完整API测试
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import sys
import os
from datetime import datetime


class StandaloneAPITester:
    """独立API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.created_ids = {
            'alerts': [],
            'entities': [],
            'verdicts': []
        }
    
    def http_request(self, url: str, method: str = "GET", data: dict = None, headers: dict = None):
        """发送HTTP请求"""
        if headers is None:
            headers = {'Content-Type': 'application/json'}
        
        try:
            if data:
                data = json.dumps(data).encode('utf-8')
            
            req = urllib.request.Request(url, data=data, headers=headers, method=method)
            
            with urllib.request.urlopen(req, timeout=10) as response:
                content = response.read().decode('utf-8')
                return {
                    'status_code': response.status,
                    'content': content,
                    'json': json.loads(content) if content else None
                }
                
        except urllib.error.HTTPError as e:
            return {
                'status_code': e.code,
                'content': e.read().decode('utf-8') if hasattr(e, 'read') else str(e),
                'json': None
            }
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def test_connection(self):
        """测试服务器连接"""
        print("🔍 检查服务器连接...")
        
        for attempt in range(3):
            response = self.http_request(f"{self.base_url}/health")
            
            if response and response['status_code'] == 200:
                print(f"✅ 连接成功: {response['json']['service']}")
                return True
            elif attempt < 2:
                print(f"⚠️ 连接失败，重试中...")
                import time
                time.sleep(2)
        
        print(f"❌ 服务器连接失败")
        return False
    
    def test_system_health(self):
        """测试系统健康状态"""
        print("\n🔍 系统健康检查...")
        
        # 测试基础端点
        endpoints = [
            ("/", "根路径"),
            ("/health", "健康检查"),
            ("/health/database", "数据库"),
            ("/api/v1/system/stats", "系统统计")
        ]
        
        for endpoint, name in endpoints:
            response = self.http_request(f"{self.base_url}{endpoint}")
            if response and response['status_code'] == 200:
                if endpoint == "/api/v1/system/stats":
                    node_count = response['json'].get('total_nodes', 0)
                    print(f"✅ {name}: 节点数 {node_count}")
                else:
                    print(f"✅ {name}: 正常")
            else:
                print(f"❌ {name}: 失败")
                return False
        
        return True
    
    def test_alerts_api(self):
        """测试告警API"""
        print("\n📋 告警API测试...")
        
        # 创建告警
        alert_data = {
            "alert_detail_id": "TEST-STANDALONE-001",
            "alert_name": "独立测试告警",
            "alert_message": "API测试用告警",
            "vulnerabilities": "测试漏洞",
            "product_name": "测试设备",
            "confidence_score": 90,
            "impact": "High",
            "risk": "Critical",
            "severity": "High",
            "status": "New",
            "comment": "standalone_test",
            "raw_data": json.dumps({"test_type": "standalone_test"}),
            "data_tag": "testing",
            "evidence_artifacts": [
                {
                    "id": "evidence-standalone-001",
                    "type": "web_attack",
                    "is_alert_trigger": True,
                    "include_payload": True,
                    "src_endpoint": {"ip": "*************"}
                }
            ]
        }
        
        # 创建
        response = self.http_request(f"{self.base_url}/api/v1/alerts/", "POST", alert_data)
        if not response or response['status_code'] != 200:
            print(f"❌ 创建告警失败: {response}")
            return False
        
        alert_id = response['json']["alert_detail_id"]
        self.created_ids['alerts'].append(alert_id)
        print(f"✅ 创建成功: {alert_id}")
        
        # 获取
        response = self.http_request(f"{self.base_url}/api/v1/alerts/{alert_id}")
        if not response or response['status_code'] != 200:
            print(f"❌ 获取告警失败")
            return False
        print("✅ 获取成功")
        
        # 更新
        update_data = {"comment": "更新测试", "severity": "Critical"}
        response = self.http_request(f"{self.base_url}/api/v1/alerts/{alert_id}", "PUT", update_data)
        if not response or response['status_code'] != 200:
            print(f"❌ 更新告警失败")
            return False
        print("✅ 更新成功")
        
        return True
    
    def test_entities_api(self):
        """测试实体API"""
        print("\n🎯 实体API测试...")
        
        # 创建实体
        entity_data = {
            "entity_type": "NetworkEndpointEntity",
            "name": "测试网络端点",
            "properties": {
                "ip": "*************",
                "hostname": "test.example.com",
                "test_marker": "standalone_test",
                "port": 80,
                "protocol": "TCP"
            },
            "description": "API测试实体",
            "risk_score": 80,
            "data_tag": "testing"
        }
        
        # 创建
        response = self.http_request(f"{self.base_url}/api/v1/entities/", "POST", entity_data)
        if not response or response['status_code'] != 200:
            print(f"❌ 创建实体失败: {response}")
            return False
        
        entity_id = response['json']["entity_id"]
        self.created_ids['entities'].append(entity_id)
        print(f"✅ 创建成功: {entity_id}")
        
        # 获取
        response = self.http_request(f"{self.base_url}/api/v1/entities/{entity_id}")
        if not response or response['status_code'] != 200:
            print(f"❌ 获取实体失败")
            return False
        print("✅ 获取成功")
        
        # 搜索
        search_url = f"{self.base_url}/api/v1/entities/?entity_type=NetworkEndpointEntity"
        response = self.http_request(search_url)
        if not response or response['status_code'] != 200:
            print(f"❌ 搜索实体失败")
            return False
        print("✅ 搜索成功")
        
        return True
    
    def test_verdicts_api(self):
        """测试研判API"""
        print("\n⚖️ 研判API测试...")
        
        if not self.created_ids['alerts']:
            print("⚠️ 跳过研判测试（无告警数据）")
            return True
        
        # 创建研判
        verdict_data = {
            "type_id": 2,
            "type": "人工研判",
            "label": 2,
            "reason": "测试确认攻击",
            "commiter": "测试分析师",
            "comment": "standalone_test",
            "data_tag": "testing"
        }
        
        # 创建
        response = self.http_request(f"{self.base_url}/api/v1/verdicts/", "POST", verdict_data)
        if not response or response['status_code'] != 200:
            print(f"❌ 创建研判失败: {response}")
            return False
        
        verdict_id = response['json']["verdict_id"]
        self.created_ids['verdicts'].append(verdict_id)
        print(f"✅ 创建成功: {verdict_id}")
        
        # 为告警添加研判
        alert_id = self.created_ids['alerts'][0]
        response = self.http_request(f"{self.base_url}/api/v1/alerts/{alert_id}/verdicts", "POST", verdict_data)
        if not response or response['status_code'] != 200:
            print(f"❌ 关联研判失败: {response}")
            return False
        print("✅ 关联成功")
        
        return True
    
    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        try:
            headers = {"X-Testing": "true", "Content-Type": "application/json"}
            os.environ["TESTING"] = "true"
            
            # 优先级清理方式
            cleanup_methods = [
                (f"{self.base_url}/api/v1/system/test/clean?data_tag=testing", "标签"),
                (f"{self.base_url}/api/v1/system/test/clean?test_markers=standalone_test", "标识"),
                (f"{self.base_url}/api/v1/system/test/clean", "通用")
            ]
            
            for cleanup_url, method_name in cleanup_methods:
                response = self.http_request(cleanup_url, "DELETE", headers=headers)
                if response and response['status_code'] == 200:
                    result = response['json']
                    print(f"✅ 清理成功 ({method_name}方式)")
                    print(f"📊 清理: 节点{len(result.get('cleaned_nodes', {}))}, 关系{result.get('cleaned_relationships', 0)}")
                    
                    # 验证清理效果
                    remaining = self._check_remaining_data()
                    if remaining:
                        print(f"⚠️ 部分数据未清理: {remaining}")
                    else:
                        print("✅ 数据清理完整")
                    return
                elif method_name != "通用":
                    print(f"⚠️ {method_name}清理失败，尝试下一种方式...")
            
            print("❌ 所有清理方式均失败")
            self._print_manual_cleanup_info()
                
        except Exception as e:
            print(f"⚠️ 清理异常: {e}")
            self._print_manual_cleanup_info()
    
    def _check_remaining_data(self):
        """检查剩余数据"""
        remaining = []
        
        for alert_id in self.created_ids['alerts']:
            if self.http_request(f"{self.base_url}/api/v1/alerts/{alert_id}")['status_code'] == 200:
                remaining.append(f"告警{alert_id}")
        
        for entity_id in self.created_ids['entities']:
            if self.http_request(f"{self.base_url}/api/v1/entities/{entity_id}")['status_code'] == 200:
                remaining.append(f"实体{entity_id}")
        
        for verdict_id in self.created_ids['verdicts']:
            if self.http_request(f"{self.base_url}/api/v1/verdicts/{verdict_id}")['status_code'] == 200:
                remaining.append(f"研判{verdict_id}")
        
        return remaining
    
    def _print_manual_cleanup_info(self):
        """打印手动清理信息"""
        print("💡 手动清理参考:")
        total = sum(len(ids) for ids in self.created_ids.values())
        if total > 0:
            print(f"  创建数据: 告警{len(self.created_ids['alerts'])}, 实体{len(self.created_ids['entities'])}, 研判{len(self.created_ids['verdicts'])}")
            print("  Cypher查询:")
            print("    MATCH (n) WHERE n.data_tag = 'testing' DETACH DELETE n")
            print("    MATCH (n) WHERE n.comment CONTAINS 'standalone_test' DETACH DELETE n")
    
    def run_all_tests(self):
        """运行完整测试流程"""
        print("🚀 AlertGraph API 测试")
        print("=" * 50)
        
        try:
            os.environ["TESTING"] = "true"
            
            # 执行测试序列
            test_sequence = [
                self.test_connection,
                self.test_system_health,
                self.test_alerts_api,
                self.test_entities_api,
                self.test_verdicts_api
            ]
            
            for test_func in test_sequence:
                if not test_func():
                    return False
            
            print("\n" + "=" * 50)
            print("🎉 所有测试通过！")
            total_created = sum(len(ids) for ids in self.created_ids.values())
            print(f"测试数据: {total_created}项 (告警{len(self.created_ids['alerts'])}, 实体{len(self.created_ids['entities'])}, 研判{len(self.created_ids['verdicts'])})")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")
            return False
        finally:
            self.cleanup_test_data()
            if "TESTING" in os.environ:
                del os.environ["TESTING"]


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AlertGraph API 独立测试")
    parser.add_argument("--url", default="http://localhost:8000", help="API服务器地址")
    
    args = parser.parse_args()
    
    start_time = datetime.now()
    print(f"⏰ 开始时间: {start_time.strftime('%H:%M:%S')}")
    
    tester = StandaloneAPITester(args.url)
    success = tester.run_all_tests()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    print(f"⏰ 结束时间: {end_time.strftime('%H:%M:%S')} (耗时 {duration:.1f}s)")
    
    if success:
        print("🎉 测试完成！")
        sys.exit(0)
    else:
        print("❌ 测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    main() 