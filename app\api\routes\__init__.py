"""
API路由包

包含所有API路由模块
"""

from fastapi import APIRouter
from .alerts import router as alerts_router
from .entities import router as entities_router
from .verdicts import router as verdicts_router, alerts_verdicts_router
from .system import router as system_router

# 创建API路由器
api_router = APIRouter()

# 包含所有路由
api_router.include_router(alerts_router)
api_router.include_router(entities_router)
api_router.include_router(verdicts_router)
api_router.include_router(alerts_verdicts_router)
api_router.include_router(system_router)

__all__ = ["api_router"] 