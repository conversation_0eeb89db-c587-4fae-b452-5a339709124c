"""
数据标签工具类

处理data_tag的传播、验证和管理逻辑
"""

import os
from typing import Optional, Dict, Any
from app.models.base import DataTag
from app.utils.logger import logger


class TagManager:
    """数据标签管理器"""
    
    @staticmethod
    def get_current_tag() -> DataTag:
        """
        获取当前环境的数据标签
        优先级：API参数 > 环境变量 > 默认值
        """
        # 检查环境变量
        env_tag = os.getenv("DATA_TAG", "").lower()
        testing_env = os.getenv("TESTING", "").lower() in ("true", "1", "yes")
        
        if testing_env:
            return DataTag.TESTING
        elif env_tag:
            try:
                return DataTag(env_tag)
            except ValueError:
                logger.warning(f"无效的DATA_TAG环境变量值: {env_tag}, 使用默认值")
        
        return DataTag.PRODUCTION
    
    @staticmethod
    def resolve_tag(
        explicit_tag: Optional[DataTag] = None,
        parent_tag: Optional[str] = None,
        default_tag: Optional[DataTag] = None
    ) -> DataTag:
        """
        解析数据标签，优先级：显式标签 > 父级标签 > 默认标签 > 环境标签
        
        Args:
            explicit_tag: 明确指定的标签
            parent_tag: 父级实体的标签
            default_tag: 默认标签
            
        Returns:
            解析后的数据标签
        """
        # 1. 显式指定的标签最高优先级
        if explicit_tag:
            return explicit_tag
        
        # 2. 继承父级标签
        if parent_tag:
            try:
                return DataTag(parent_tag)
            except ValueError:
                logger.warning(f"无效的父级标签: {parent_tag}")
        
        # 3. 使用默认标签
        if default_tag:
            return default_tag
        
        # 4. 使用环境标签
        return TagManager.get_current_tag()
    
    @staticmethod
    def add_tag_to_properties(properties: Dict[str, Any], tag: DataTag) -> Dict[str, Any]:
        """
        将data_tag添加到节点属性中
        
        Args:
            properties: 节点属性字典
            tag: 数据标签
            
        Returns:
            添加了data_tag的属性字典
        """
        properties = properties.copy()
        properties["data_tag"] = tag.value
        return properties
    
    @staticmethod
    def add_tag_to_relationship_properties(
        properties: Dict[str, Any], 
        tag: DataTag
    ) -> Dict[str, Any]:
        """
        将data_tag添加到关系属性中
        
        Args:
            properties: 关系属性字典
            tag: 数据标签
            
        Returns:
            添加了data_tag的属性字典
        """
        properties = properties.copy()
        properties["data_tag"] = tag.value
        return properties
    
    @staticmethod
    def validate_tag_consistency(
        source_tag: str, 
        target_tag: str, 
        operation: str = "关联操作"
    ) -> bool:
        """
        验证标签一致性，确保相关实体使用相同的标签
        
        Args:
            source_tag: 源实体标签
            target_tag: 目标实体标签
            operation: 操作描述
            
        Returns:
            是否一致
        """
        if source_tag != target_tag:
            logger.warning(
                f"{operation}标签不一致", 
                source_tag=source_tag, 
                target_tag=target_tag
            )
            return False
        return True
    
    @staticmethod
    def create_tag_filter_query(tag: Optional[DataTag] = None) -> str:
        """
        创建基于标签的过滤查询条件
        
        Args:
            tag: 要过滤的标签，None表示不过滤
            
        Returns:
            Cypher查询条件
        """
        if tag is None:
            return ""
        
        return f"AND n.data_tag = '{tag.value}'"
    
    @staticmethod
    def get_cleanup_query(tag: DataTag) -> str:
        """
        生成清理指定标签数据的Cypher查询
        
        Args:
            tag: 要清理的数据标签
            
        Returns:
            清理查询语句
        """
        return f"""
        MATCH (n)
        WHERE n.data_tag = '{tag.value}'
        OPTIONAL MATCH (n)-[r]-()
        WHERE r.data_tag = '{tag.value}'
        DELETE r, n
        RETURN count(DISTINCT n) as deleted_nodes, count(DISTINCT r) as deleted_relationships
        """
    
    @staticmethod
    def is_testing_tag(tag: str) -> bool:
        """
        判断是否为测试相关标签
        
        Args:
            tag: 标签值
            
        Returns:
            是否为测试标签
        """
        testing_tags = {
            DataTag.TESTING.value,
            DataTag.DEVELOPMENT.value,
            DataTag.UNIT_TEST.value,
            DataTag.INTEGRATION.value,
            DataTag.BENCHMARK.value
        }
        return tag in testing_tags 