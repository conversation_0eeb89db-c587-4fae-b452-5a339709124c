services:
  neo4j:
    image: neo4j:5.26.7-community
    container_name: neo4j-community
    restart: unless-stopped #容器会在 异常退出 或 主机重启后自动启动，除非手动停止容器
    ports:
      - "7474:7474"  # HTTP 界面
      - "7687:7687"  # Bolt 协议
    environment:
      NEO4J_AUTH: neo4j/firstpassword  # 第一次启动设置的密码（无风险）
      NEO4J_PLUGINS: '["apoc"]'   # 启用 APOC 插件
      NEO4J_dbms_security_procedures_unrestricted: 'apoc.*,gds.*'
      NEO4J_dbms_security_procedures_allowlist: 'apoc.*,gds.*'
      NEO4J_apoc_export_file_enabled: "true"
      NEO4J_apoc_import_file_enabled: "true"
      NEO4J_apoc_import_file_use__neo4j__config: "true"
    volumes:
      - ./data:/data         # 数据持久化
      - ./logs:/logs         # 日志持久化
      - ./import:/import     # 可选：用于 LOAD CSV
      - ./plugins:/plugins   # 插件目录（Neo4j 可能会自动下载 APOC 插件）

