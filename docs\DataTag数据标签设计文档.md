# AlertGraph 数据标签(DataTag)设计文档

## 1. 设计目标

为了更好地管理测试数据和正式数据，AlertGraph引入了统一的数据标签(DataTag)字段，实现以下目标：

- **环境隔离**: 明确区分测试、开发、正式等不同环境的数据
- **精确清理**: 支持按标签精确清理特定环境的数据，避免误删
- **数据追踪**: 便于追踪数据来源和生命周期管理
- **标签传播**: 确保相关联的数据（告警→证据→实体→设备）使用相同标签

## 2. 数据标签定义

### 2.1 标签枚举

```python
class DataTag(str, Enum):
    """数据标签枚举 - 用于区分数据环境"""
    PRODUCTION = "production"      # 正式环境数据
    TESTING = "testing"           # 测试环境数据
    DEVELOPMENT = "development"   # 开发环境数据
    DEMO = "demo"                # 演示数据
    BENCHMARK = "benchmark"      # 性能测试数据
    INTEGRATION = "integration"  # 集成测试数据
    UNIT_TEST = "unit_test"      # 单元测试数据
```

### 2.2 标签说明

| 标签 | 用途 | 典型场景 |
|------|------|----------|
| `production` | 正式环境数据 | 生产系统的真实告警数据 |
| `testing` | 测试环境数据 | 手动测试、功能验证 |
| `development` | 开发环境数据 | 开发阶段的调试数据 |
| `demo` | 演示数据 | 产品演示、培训使用 |
| `benchmark` | 性能测试数据 | 压力测试、性能基准测试 |
| `integration` | 集成测试数据 | 自动化集成测试 |
| `unit_test` | 单元测试数据 | 单元测试产生的数据 |

## 3. 标签传播机制

### 3.1 传播规则

数据标签在创建关联数据时会自动传播，确保相关数据使用相同标签：

```
AlertDetail (data_tag: testing)
    ↓ 创建证据时传播
Evidence (data_tag: testing)
    ↓ 提取实体时传播  
Entity (data_tag: testing)
    ↓ 创建设备时传播
Device (data_tag: testing)
```

### 3.2 传播优先级

标签解析优先级：**显式标签 > 父级标签 > 默认标签 > 环境标签**

1. **显式标签**: API请求中明确指定的`data_tag`
2. **父级标签**: 继承自父级实体的标签（如实体继承证据的标签）
3. **默认标签**: 服务方法指定的默认标签
4. **环境标签**: 从环境变量`DATA_TAG`或`TESTING`读取

### 3.3 实现示例

```python
# 创建告警时明确指定标签
alert_data = {
    "alert_detail_id": "TEST-001",
    "alert_name": "测试告警",
    "data_tag": "testing",  # 明确指定
    "evidence_artifacts": [...]
}

# 标签会自动传播到：
# - Evidence节点: data_tag = "testing"
# - 提取的实体: data_tag = "testing"  
# - 创建的设备: data_tag = "testing"
# - 所有关系: data_tag = "testing"
```

## 4. 数据模型集成

### 4.1 节点标签

所有核心节点都包含`data_tag`字段：

- **AlertDetail**: 告警细节节点
- **Verdict**: 研判记录节点
- **Evidence**: 证据信息节点
- **Device**: 设备信息节点
- **所有实体节点**: NetworkEndpointEntity、FileEntity等

### 4.2 关系标签

所有关系也包含`data_tag`属性：

- **HAS_EVIDENCE**: AlertDetail → Evidence
- **RELATES_TO**: Evidence → Entity
- **GENERATED_BY**: AlertDetail → Device
- **HAS_VERDICT**: AlertDetail → Verdict

### 4.3 属性结构

```cypher
# 节点示例
(:AlertDetail {
    vid: "alert-123",
    alert_detail_id: "TEST-001", 
    data_tag: "testing",
    ...其他属性
})

# 关系示例
(:AlertDetail)-[:HAS_EVIDENCE {
    created_time: "2024-12-09T16:30:00",
    data_tag: "testing"
}]->(:Evidence)
```

## 5. API接口支持

### 5.1 创建时指定标签

```python
# 创建告警
POST /api/v1/alerts/
{
    "alert_detail_id": "TEST-001",
    "data_tag": "testing",
    ...
}

# 创建实体
POST /api/v1/entities/
{
    "entity_type": "NetworkEndpointEntity",
    "data_tag": "testing",
    ...
}
```

### 5.2 按标签查询

```python
# 查询特定标签的告警
GET /api/v1/alerts/?data_tag=testing

# 查询特定标签的实体
GET /api/v1/entities/?data_tag=production
```

### 5.3 标签统计

```python
# 获取系统中所有标签的统计信息
GET /api/v1/system/tags

# 响应示例
{
    "testing": {
        "total": 45,
        "by_type": {
            "AlertDetail": 10,
            "Evidence": 10,
            "NetworkEndpointEntity": 15,
            "Device": 5,
            "Verdict": 5
        }
    },
    "production": {
        "total": 128,
        "by_type": {
            "AlertDetail": 32,
            "Evidence": 32, 
            "NetworkEndpointEntity": 64
        }
    }
}
```

## 6. 数据清理功能

### 6.1 清理API

```python
# 按标签清理
DELETE /api/v1/system/test/clean?data_tag=testing
Headers: X-Testing: true

# 按测试标识清理
DELETE /api/v1/system/test/clean?test_markers=standalone_test
Headers: X-Testing: true

# 通用测试数据清理（清理所有测试相关标签）
DELETE /api/v1/system/test/clean
Headers: X-Testing: true
```

### 6.2 清理策略

1. **精确清理**: 指定标签，只清理该标签的数据
2. **标识符清理**: 基于节点属性中的测试标识符
3. **批量清理**: 自动识别并清理所有测试相关标签的数据

### 6.3 安全机制

- 必须设置`X-Testing: true`请求头
- 或者设置`TESTING=true`环境变量
- 防止误删正式环境数据

## 7. 环境变量配置

### 7.1 支持的环境变量

```bash
# 设置默认数据标签
export DATA_TAG=testing

# 设置为测试环境（会自动使用testing标签）
export TESTING=true
```

### 7.2 标签解析逻辑

```python
def get_current_tag() -> DataTag:
    # 1. 检查TESTING环境变量
    if os.getenv("TESTING", "").lower() in ("true", "1", "yes"):
        return DataTag.TESTING
    
    # 2. 检查DATA_TAG环境变量
    env_tag = os.getenv("DATA_TAG", "").lower()
    if env_tag:
        try:
            return DataTag(env_tag)
        except ValueError:
            # 无效标签，使用默认值
            pass
    
    # 3. 默认为production
    return DataTag.PRODUCTION
```

## 8. 最佳实践

### 8.1 开发环境

```python
# 设置环境变量
export DATA_TAG=development

# 或在代码中明确指定
alert_data = {
    "data_tag": "development",
    ...
}
```

### 8.2 测试环境

```python
# 自动化测试
export TESTING=true

# 手动测试
alert_data = {
    "data_tag": "testing",
    "comment": "手动测试 - test_case_001",
    ...
}
```

### 8.3 正式环境

```python
# 不设置环境变量，默认为production
# 或明确指定
alert_data = {
    "data_tag": "production",
    ...
}
```

### 8.4 数据清理

```python
# 测试完成后清理
import requests

headers = {"X-Testing": "true"}
response = requests.delete(
    "http://localhost:8000/api/v1/system/test/clean?data_tag=testing",
    headers=headers
)
```

## 9. 查询示例

### 9.1 Cypher查询

```cypher
-- 查询所有测试数据
MATCH (n) WHERE n.data_tag = 'testing' RETURN n

-- 查询测试告警及其关联实体
MATCH (a:AlertDetail {data_tag: 'testing'})-[:HAS_EVIDENCE]->(e:Evidence)-[:RELATES_TO]->(entity)
RETURN a, e, entity

-- 统计各标签的数据量
MATCH (n) 
WHERE n.data_tag IS NOT NULL
RETURN n.data_tag as tag, labels(n) as type, count(n) as count
ORDER BY tag, type
```

### 9.2 清理查询

```cypher
-- 清理特定标签的所有数据
MATCH (n) WHERE n.data_tag = 'testing'
OPTIONAL MATCH (n)-[r]-()
WHERE r.data_tag = 'testing' OR r.data_tag IS NULL
DELETE r, n

-- 清理包含测试标识的数据
MATCH (n)
WHERE n.comment CONTAINS 'test' 
   OR n.alert_detail_id CONTAINS 'TEST'
DETACH DELETE n
```

## 10. 注意事项

### 10.1 标签一致性

- 确保相关联的数据使用相同标签
- 避免跨标签的数据关联

### 10.2 清理安全

- 清理前务必确认标签和环境
- 建议先查询再删除
- 正式环境禁用清理API

### 10.3 性能考虑

- 为`data_tag`字段建立索引
- 大批量清理时分批执行
- 监控清理操作的性能影响

### 10.4 数据一致性

- 创建关联数据时检查标签一致性
- 记录标签不一致的警告日志
- 定期检查数据标签的完整性 