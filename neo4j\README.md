# 结构介绍
``` bash
├── docker-compose.yml
├── data/         # 数据文件（Neo4j 自动写入）
├── logs/         # 日志文件
├── import/       # CSV 或导入文件目录
└── plugins/      # APOC 插件目录（Neo4j 启动时自动填充）
```

# 启动

``` bash
sudo docker compose up -d
```

## 确认插件安装成功

``` sql
RETURN apoc.version()
```

![alt text](../assets/image.png)


# 更新说明

## neo4j版本

参考docker hub公布的neo4j版本，选择社区版
> https://hub.docker.com/_/neo4j/tags

## APOC插件版本

参考github releases的版本，选择与neo4j匹配的版本
> https://github.com/neo4j/apoc/releases

## 更新方法

1. 修改 docker-compose.yml

``` yml
image: neo4j:5.26.7-community # 修改版本号
```

2. 停止并删除旧容器

``` bash
sudo docker compose down
```

3. 启动新容器
``` bash
sudo docker compose up -d
```