"""
AlertGraph 应用入口点

运行图数据库管理与API服务
"""

import uvicorn
from app.core.config import settings


def check_dependencies():
    """检查启动依赖"""
    try:
        from app.database.connection import ensure_connection_ready
        ensure_connection_ready()
        return True
    except Exception as e:
        print(f"❌ Neo4j连接失败: {e}")
        print("💡 请确保Neo4j服务正在运行且配置正确")
        return False


def main():
    """启动应用服务器"""
    print(f"🚀 启动 {settings.app_name} v{settings.app_version}")
    print(f"📖 API文档: http://localhost:8000{settings.docs_url}")
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 启动失败")
        return
    
    print("✅ 组件检查通过，启动服务器...")
    
    # 启动FastAPI应用
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
