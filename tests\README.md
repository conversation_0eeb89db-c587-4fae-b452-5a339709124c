# AlertGraph API 测试方案

## 概述

这是AlertGraph项目的完整API测试方案，包含所有API端点的功能测试和data_tag验证测试。

## 测试文件结构

```
tests/
├── README.md                      # 测试说明文档
├── test_config.py                 # 测试配置和工具类
├── test_api_complete.py           # 完整API功能测试
├── test_data_tag_validation.py    # data_tag验证测试
├── run_tests.py                   # 测试运行器
├── standalone_test.py             # 简化版测试（原有）
└── test_report_*.json             # 测试报告文件（运行后生成）
```

## 测试特性

### ✅ 完整API覆盖
- **告警管理API**: 创建、查询、更新、批量操作
- **研判管理API**: 创建研判、为告警添加研判、查询研判
- **系统管理API**: 健康检查、统计信息、测试环境管理

### ✅ data_tag验证
- 验证所有节点（告警、实体、研判）都正确携带`data_tag="testing"`
- 验证关系也正确传递data_tag
- 生成详细的验证报告

### ✅ 自动清理
- 所有测试数据都使用唯一标识符
- 测试完成后自动清理，不影响生产数据
- 支持手动清理和批量清理

### ✅ 详细报告
- 生成JSON格式的测试报告
- 包含请求统计、成功率、耗时等信息
- 支持错误详情和调试信息

## 使用方法

### 前置条件

1. **启动AlertGraph服务**
   ```bash
   cd /path/to/AlertGraph
   python main.py
   ```

2. **确保服务正常运行**
   - 访问 http://localhost:8000/docs 确认API文档可用
   - 访问 http://localhost:8000/api/v1/system/health 确认健康检查正常

### 运行完整测试

```bash
# 方法1: 使用测试运行器（推荐）
cd /path/to/AlertGraph
python tests/run_tests.py

# 方法2: 直接运行测试脚本
python tests/test_api_complete.py

# 方法3: 运行简化版测试
python tests/standalone_test.py
```

### 运行data_tag验证测试

```bash
python tests/test_data_tag_validation.py
```

### 运行快速测试

```bash
# 快速验证基本功能和data_tag
python tests/quick_test.py

# 指定服务器地址
python tests/quick_test.py --url=http://localhost:8000
```

## 测试配置

可以在 `tests/test_config.py` 中修改测试配置：

```python
class TestConfig:
    API_BASE_URL = "http://localhost:8000"  # API服务地址
    TEST_TAG = "testing"                    # 测试数据标识（系统支持的枚举值）
    TEST_ALERT_COUNT = 5                    # 测试告警数量
    BATCH_SIZE = 3                          # 批量操作大小
    AUTO_CLEANUP = True                     # 是否自动清理
```

## 测试流程

### 完整API测试流程

1. **系统健康检查** - 验证服务可用性
2. **加载测试数据** - 从真实数据格式生成测试数据
3. **创建单个告警** - 测试单个告警创建API
4. **批量创建告警** - 测试批量告警创建API
5. **查询告警** - 测试告警查询和详情获取API
6. **更新告警** - 测试告警更新API
7. **创建研判记录** - 测试独立研判创建API
8. **为告警添加研判** - 测试告警研判关联API
9. **查询研判记录** - 测试研判查询API
10. **获取告警实体** - 测试实体关联查询API
11. **清理测试数据** - 自动清理所有测试数据

### data_tag验证流程

1. **创建测试告警** - 包含复杂的证据结构
2. **添加测试研判** - 为告警添加研判记录
3. **验证告警data_tag** - 检查告警节点的data_tag
4. **验证实体data_tag** - 检查所有衍生实体的data_tag
5. **验证研判data_tag** - 检查研判记录的关联
6. **生成验证报告** - 详细的验证结果报告
7. **清理测试数据** - 清理验证过程中的数据

### 快速测试流程

1. **服务器连接测试** - 验证API服务可用
2. **创建告警测试** - 创建告警并验证data_tag
3. **添加研判测试** - 为告警添加研判记录
4. **获取实体测试** - 获取实体并验证data_tag
5. **清理测试数据** - 自动清理测试数据

## 测试数据

### 数据来源

测试优先使用 `assets/data/api_format_alerts.json` 中的真实格式数据，如果文件不存在则生成默认测试数据。

### 数据特征

- **唯一标识**: 每个测试数据都有唯一的`alert_detail_id`
- **测试标记**: 所有数据都携带`data_tag="testing"`（系统支持的枚举值）
- **时间戳**: 包含创建时间和测试标识符
- **完整结构**: 包含证据、实体、关系等完整数据结构

### 数据清理

```python
# 测试数据标识符格式
alert_detail_id = "TEST-API-{timestamp}-{index:03d}"

# 清理方式
1. 自动清理：测试完成后自动调用清理API
2. 手动清理：调用 DELETE /api/v1/system/test/clean
3. 标识符清理：基于test_markers进行精确清理
```

## 故障排查

### 常见问题

1. **服务器连接失败**
   ```
   ❌ 无法连接到服务器: Connection refused
   ```
   **解决方案**: 确保AlertGraph服务正在运行

2. **测试数据创建失败**
   ```
   ❌ 创建告警失败: 数据验证失败
   ```
   **解决方案**: 检查测试数据格式和必填字段

3. **清理失败**
   ```
   ⚠️ 清理失败，请手动清理测试数据
   ```
   **解决方案**: 手动调用清理API或重启服务

### 手动清理

如果自动清理失败，可以手动清理：

```bash
# 调用清理API
curl -X DELETE "http://localhost:8000/api/v1/system/test/clean" \
  -H "X-Testing: true" \
  -H "Content-Type: application/json"
```

## 注意事项

1. **测试环境**: 请在测试环境中运行，避免影响生产数据
2. **数据清理**: 测试完成后务必确认数据已清理
3. **并发测试**: 避免同时运行多个测试实例
4. **资源限制**: 注意批量操作的数量限制（最多1000条）
5. **data_tag验证**: 确保所有节点和关系都正确携带`data_tag="testing"`标识

---

**更新时间**: 2024-12-09
**版本**: v1.0.0
**维护者**: AlertGraph开发团队