# AlertGraph API 测试

简化的API测试工具，用于快速验证AlertGraph后端API功能。

## 文件说明

- `standalone_test.py` - 核心测试文件（推荐使用，无外部依赖）
- `README.md` - 本说明文件

## 快速使用

```bash
# 确保AlertGraph后端在运行
cd tests
python standalone_test.py
```

## 测试内容

- ✅ 服务器连接测试
- ✅ 健康检查和数据库状态
- ✅ 告警API（创建、获取、更新）
- ✅ 实体API（创建、获取、搜索）
- ✅ 研判API（创建、关联告警）
- ✅ 自动清理测试数据

## 特点

- 仅使用Python标准库
- 自动创建测试数据，带有特殊标识
- 测试完成后自动清理
- 详细输出测试过程

## 注意事项

- 需要后端服务正在运行（默认 http://localhost:8000）
- 测试数据包含 "standalone_test" 标识便于清理
- 支持自定义服务器地址：`python standalone_test.py --url http://custom:8000` 